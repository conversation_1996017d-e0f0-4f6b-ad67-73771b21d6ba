server {
    listen 80;
    server_name oliviqo.com www.oliviqo.com;
    
    # Redirect all HTTP requests to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name oliviqo.com www.oliviqo.com;

    # Add resolver for dynamic proxy_pass
    resolver 127.0.0.1 valid=30s;

    # Internal location for Python routing service
    location = /auth-route {
        internal;
        proxy_pass http://127.0.0.1:3000/route;
        proxy_pass_request_body off;
        proxy_set_header Content-Length "";
        proxy_set_header X-Original-URI $request_uri;
        proxy_set_header X-User-Agent $http_user_agent;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_connect_timeout 2s;
        proxy_read_timeout 2s;
    }

    
    # SSL Configuration (will be managed by Certbot)
    ssl_certificate /etc/letsencrypt/live/oliviqo.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/oliviqo.com/privkey.pem;
    
    # SSL Security Headers
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Proxy Configuration
    location / {
	 # Call the Python service to determine routing
        auth_request /auth-route;
        auth_request_set $proxy_port $upstream_http_x_pro_po;
        auth_request_set $bot_type $upstream_http_x_bot_type;

        # Set port logic - use proxy_port if available, fallback to 3000
        set $final_port $bot_type;
        if ($bot_type = "") {
            set $final_port "8080";
        }

        proxy_pass http://127.0.0.1:$final_port;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # Buffering
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
	#https://oliviqo.com/
	#add_header X-Routed-Port $final_port always;
        #add_header X-Debug-Proxy-Port $proxy_port always;
        #add_header X-Debug-Bot-Type $bot_type always;
        #add_header X-Debug-Raw-Port $upstream_http_x_pro_po always;
    }
    
    # Handle large file uploads
    client_max_body_size 100M;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
