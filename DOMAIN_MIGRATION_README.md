# WordPress Domain Migration Scripts

This repository contains scripts to migrate a WordPress site deployed with `setup_wordpress_server.sh` to a new domain, including SSL certificate setup and database URL updates.

## Scripts Overview

### 1. `move_wordpress_domain.sh`
Main migration script that handles the complete domain change process.

### 2. `rollback_domain_migration.sh`
Rollback script to restore the site to its previous domain if something goes wrong.

## Prerequisites

- WordPress site deployed using the `setup_wordpress_server.sh` script
- Root access to the server
- New domain pointing to your server's IP address
- Email address for SSL certificate registration
- Python routing service running on port 3000 (for dynamic port routing)
- Nginx configuration with dynamic routing setup

## Pre-Migration Checklist

1. **DNS Setup**: Ensure your new domain is pointing to your server's IP address
2. **Python Service**: Ensure your Python routing service is running on port 3000
3. **Backup**: The script creates automatic backups, but consider additional backups
4. **Maintenance Mode**: Consider putting your site in maintenance mode during migration
5. **Testing Environment**: Test the migration process in a staging environment if possible

## Usage

### Step 1: Prepare for Migration

1. Update your DNS records to point the new domain to your server's IP
2. Wait for DNS propagation (can take up to 48 hours, but usually much faster)
3. Verify DNS propagation using online tools or `dig` command

### Step 2: Run the Migration Script

```bash
sudo ./move_wordpress_domain.sh
```

The script will:
1. Detect your current domain from Nginx configuration
2. Prompt for the new domain and email address
3. Check if Python routing service is running on port 3000
4. Verify DNS resolution
5. Create comprehensive backups
6. Update Nginx configuration (preserving dynamic routing)
7. Obtain new SSL certificates
8. Update WordPress URLs in the database
9. Perform final verification

### Step 3: Post-Migration Tasks

1. Test your website thoroughly
2. Clear any WordPress caching plugins
3. Update external services (CDN, monitoring, etc.)
4. Set up redirects from old domain if needed
5. Update any hardcoded URLs in content

## Migration Process Details

### What the Script Does

1. **Backup Creation**:
   - WordPress files (`/opt/wordpress`)
   - Nginx configuration
   - MySQL database dump
   - Stored in timestamped backup directory

2. **DNS Verification**:
   - Checks if new domain resolves to server IP
   - Warns about DNS propagation issues

3. **Nginx Configuration**:
   - Updates server_name directives
   - Preserves dynamic routing with Python service integration
   - Creates temporary HTTP-only config for SSL setup
   - Applies SSL configuration after certificate generation

4. **SSL Certificate Setup**:
   - Obtains Let's Encrypt certificates for new domain
   - Configures automatic renewal
   - Optionally removes old domain certificates

5. **WordPress Database Updates**:
   - Updates `home` and `siteurl` options
   - Performs search-replace for all domain references
   - Uses WP-CLI for safe database operations

### Backup Location

Backups are stored in `/opt/wordpress_backup_YYYYMMDD_HHMMSS/` containing:
- `wordpress_files/` - Complete WordPress installation
- `nginx_wordpress.conf` - Original Nginx configuration with dynamic routing
- `wordpress_database.sql` - Database dump

## Rollback Process

If something goes wrong, use the rollback script:

```bash
sudo ./rollback_domain_migration.sh
```

The rollback script will:
1. Show available backups
2. Allow you to select which backup to restore
3. Restore Nginx configuration
4. Restore WordPress database
5. Optionally restore WordPress files

## Troubleshooting

### Common Issues

1. **DNS Not Propagated**:
   - Wait longer for DNS propagation
   - Use `dig yourdomain.com` to check resolution
   - Consider using `nslookup` or online DNS checkers

2. **SSL Certificate Failed**:
   - Ensure domain is accessible via HTTP first
   - Check firewall settings (ports 80, 443)
   - Verify domain ownership

3. **WordPress URLs Not Updated**:
   - Manually update using WordPress admin panel
   - Use WP-CLI: `wp option update home "https://newdomain.com"`
   - Check for cached content

4. **Python Routing Service Issues**:
   - Check if service is running: `curl http://127.0.0.1:3000/route`
   - Restart the Python routing service
   - Verify service logs for errors

5. **Database Connection Issues**:
   - Verify Docker containers are running: `docker compose ps`
   - Check database credentials in `.env` file
   - Restart containers: `docker compose restart`

### Manual Verification Commands

```bash
# Check Docker containers
cd /opt/wordpress && docker compose ps

# Test Python routing service
curl -I http://127.0.0.1:3000/route

# Test domain resolution
dig yourdomain.com

# Check SSL certificate
openssl s_client -connect yourdomain.com:443 -servername yourdomain.com

# Test website response
curl -I https://yourdomain.com

# Check Nginx configuration
nginx -t

# View WordPress URLs in database
cd /opt/wordpress
docker compose exec wordpress wp --allow-root option get home
docker compose exec wordpress wp --allow-root option get siteurl
```

## Security Considerations

1. **Backup Security**: Backup files contain sensitive data - secure them appropriately
2. **SSL Certificates**: Old certificates remain valid until expiry
3. **Database Passwords**: Consider updating database passwords after migration
4. **WordPress Admin**: Update WordPress admin passwords after migration

## File Locations

- WordPress files: `/opt/wordpress/`
- Nginx configuration: `/etc/nginx/sites-enabled/wordpress`
- Nginx available config: `/etc/nginx/sites-available/wordpress`
- Python routing service: Port 3000
- SSL certificates: `/etc/letsencrypt/live/DOMAIN/`
- Backups: `/opt/wordpress_backup_*/`

## Support

If you encounter issues:

1. Check the backup directory for restore options
2. Review Nginx and Docker logs
3. Verify DNS and SSL certificate status
4. Use the rollback script if necessary

## Important Notes

- Always test in a staging environment first
- Keep backups until you're confident the migration is successful
- Monitor your site for 24-48 hours after migration
- Update any external monitoring or backup services
- Consider setting up redirects from the old domain
