#!/bin/bash

# WordPress Domain Migration Rollback Script
# This script rolls back a domain migration performed by move_wordpress_domain.sh

set -e  # Exit on any error

# Configuration variables
BACKUP_DIR=""
WORDPRESS_DIR="/opt/wordpress"
NGINX_CONF="/etc/nginx/sites-enabled/wordpress"
NGINX_AVAILABLE="/etc/nginx/sites-available/wordpress"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Find available backups
find_backups() {
    echo "=== WordPress Domain Migration Rollback ==="
    echo
    
    # Look for backup directories
    BACKUP_DIRS=($(find /opt -maxdepth 1 -name "wordpress_backup_*" -type d 2>/dev/null | sort -r))
    
    if [[ ${#BACKUP_DIRS[@]} -eq 0 ]]; then
        error "No backup directories found in /opt/"
        echo "Backup directories should be named like: wordpress_backup_YYYYMMDD_HHMMSS"
        exit 1
    fi
    
    echo "Available backups:"
    for i in "${!BACKUP_DIRS[@]}"; do
        echo "  $((i+1)). ${BACKUP_DIRS[$i]}"
        if [[ -f "${BACKUP_DIRS[$i]}/nginx_wordpress.conf" ]]; then
            OLD_DOMAIN=$(grep -oP 'server_name \K[^;]*' "${BACKUP_DIRS[$i]}/nginx_wordpress.conf" | head -1 | awk '{print $1}')
            echo "     Domain: $OLD_DOMAIN"
        fi
        echo "     Created: $(stat -c %y "${BACKUP_DIRS[$i]}" | cut -d'.' -f1)"
        echo
    done
    
    while true; do
        read -p "Select backup to restore (1-${#BACKUP_DIRS[@]}): " selection
        if [[ "$selection" =~ ^[0-9]+$ ]] && [[ "$selection" -ge 1 ]] && [[ "$selection" -le ${#BACKUP_DIRS[@]} ]]; then
            BACKUP_DIR="${BACKUP_DIRS[$((selection-1))]}"
            break
        else
            error "Invalid selection. Please enter a number between 1 and ${#BACKUP_DIRS[@]}"
        fi
    done
    
    info "Selected backup: $BACKUP_DIR"
}

# Verify backup contents
verify_backup() {
    log "Verifying backup contents..."
    
    local missing_files=()
    
    if [[ ! -f "$BACKUP_DIR/nginx_wordpress.conf" ]]; then
        missing_files+=("Nginx configuration")
    fi
    
    if [[ ! -d "$BACKUP_DIR/wordpress_files" ]]; then
        missing_files+=("WordPress files")
    fi
    
    if [[ ! -f "$BACKUP_DIR/wordpress_database.sql" ]]; then
        missing_files+=("WordPress database")
    fi
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        warning "Some backup components are missing:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        echo
        read -p "Continue with partial rollback? (y/N): " continue_confirm
        if [[ ! "$continue_confirm" =~ ^[Yy]$ ]]; then
            echo "Rollback cancelled."
            exit 0
        fi
    else
        log "Backup verification completed successfully"
    fi
}

# Get confirmation
get_confirmation() {
    echo
    warning "IMPORTANT: This will restore your WordPress site to its previous state."
    warning "Any changes made after the backup was created will be lost!"
    echo
    
    if [[ -f "$BACKUP_DIR/nginx_wordpress.conf" ]]; then
        OLD_DOMAIN=$(grep -oP 'server_name \K[^;]*' "$BACKUP_DIR/nginx_wordpress.conf" | head -1 | awk '{print $1}')
        info "This will restore the site to domain: $OLD_DOMAIN"
    fi
    
    echo
    read -p "Are you sure you want to proceed with the rollback? (y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo "Rollback cancelled."
        exit 0
    fi
}

# Restore Nginx configuration
restore_nginx() {
    if [[ -f "$BACKUP_DIR/nginx_wordpress.conf" ]]; then
        log "Restoring Nginx configuration..."

        cp "$BACKUP_DIR/nginx_wordpress.conf" "$NGINX_AVAILABLE"
        ln -sf "$NGINX_AVAILABLE" "$NGINX_CONF"

        # Test Nginx configuration
        if nginx -t; then
            systemctl reload nginx
            log "Nginx configuration restored successfully"
        else
            error "Nginx configuration test failed"
            return 1
        fi
    else
        warning "Nginx configuration backup not found, skipping"
    fi
}

# Restore WordPress database
restore_database() {
    if [[ -f "$BACKUP_DIR/wordpress_database.sql" ]]; then
        log "Restoring WordPress database..."
        
        cd "$WORDPRESS_DIR"
        
        # Check if database container is running
        if docker compose ps | grep -q "wordpress_db.*Up"; then
            # Create a backup of current database before restoring
            CURRENT_BACKUP="/tmp/current_db_backup_$(date +%Y%m%d_%H%M%S).sql"
            docker compose exec -T db mysqldump -u root -prootpassword wordpress > "$CURRENT_BACKUP"
            info "Current database backed up to: $CURRENT_BACKUP"
            
            # Restore from backup
            docker compose exec -T db mysql -u root -prootpassword wordpress < "$BACKUP_DIR/wordpress_database.sql"
            log "WordPress database restored successfully"
        else
            error "WordPress database container is not running"
            return 1
        fi
    else
        warning "Database backup not found, skipping"
    fi
}

# Restore WordPress files (optional)
restore_wordpress_files() {
    if [[ -d "$BACKUP_DIR/wordpress_files" ]]; then
        read -p "Restore WordPress files? This will overwrite current files (y/N): " restore_files
        if [[ "$restore_files" =~ ^[Yy]$ ]]; then
            log "Restoring WordPress files..."
            
            # Create backup of current files
            CURRENT_FILES_BACKUP="/tmp/current_wordpress_files_$(date +%Y%m%d_%H%M%S)"
            cp -r "$WORDPRESS_DIR" "$CURRENT_FILES_BACKUP"
            info "Current WordPress files backed up to: $CURRENT_FILES_BACKUP"
            
            # Stop containers
            cd "$WORDPRESS_DIR"
            docker compose down
            
            # Restore files
            rm -rf "$WORDPRESS_DIR"
            cp -r "$BACKUP_DIR/wordpress_files" "$WORDPRESS_DIR"
            
            # Start containers
            cd "$WORDPRESS_DIR"
            docker compose up -d
            
            log "WordPress files restored successfully"
        else
            info "WordPress files restoration skipped"
        fi
    else
        warning "WordPress files backup not found, skipping"
    fi
}

# Final verification
final_verification() {
    log "Performing final verification..."
    
    # Wait for services to start
    sleep 15
    
    # Check if containers are running
    cd "$WORDPRESS_DIR"
    if docker compose ps | grep -q "Up"; then
        log "WordPress containers are running"
    else
        warning "Some WordPress containers might not be running"
    fi
    
    # Get domain from restored config
    if [[ -f "$NGINX_CONF" ]]; then
        RESTORED_DOMAIN=$(grep -oP 'server_name \K[^;]*' "$NGINX_CONF" | head -1 | awk '{print $1}')
        
        # Test if site is accessible
        if curl -s -o /dev/null -w "%{http_code}" http://$RESTORED_DOMAIN | grep -q "200\|301\|302"; then
            log "Site is accessible at: http://$RESTORED_DOMAIN"
        else
            warning "Site might not be fully ready yet"
        fi
        
        # Test HTTPS if certificates exist
        if [[ -f "/etc/letsencrypt/live/$RESTORED_DOMAIN/fullchain.pem" ]]; then
            if curl -s -o /dev/null -w "%{http_code}" https://$RESTORED_DOMAIN | grep -q "200\|301\|302"; then
                log "HTTPS is working at: https://$RESTORED_DOMAIN"
            else
                warning "HTTPS might not be working correctly"
            fi
        fi
    fi
    
    echo
    echo "=========================================="
    echo "WordPress Domain Migration Rollback Complete!"
    echo "=========================================="
    echo
    info "Rollback Summary:"
    echo "  - Backup Used: $BACKUP_DIR"
    if [[ -n "$RESTORED_DOMAIN" ]]; then
        echo "  - Restored Domain: $RESTORED_DOMAIN"
        echo "  - Site URL: https://$RESTORED_DOMAIN"
        echo "  - Admin URL: https://$RESTORED_DOMAIN/wp-admin"
    fi
    echo
    info "Next Steps:"
    echo "  1. Test your website thoroughly"
    echo "  2. Verify all functionality is working"
    echo "  3. Check WordPress admin panel"
    echo "  4. Update DNS if necessary"
    echo
}

# Main execution
main() {
    echo "Starting WordPress Domain Migration Rollback..."
    echo
    
    check_root
    find_backups
    verify_backup
    get_confirmation
    
    restore_nginx
    restore_database
    restore_wordpress_files
    final_verification
    
    log "Rollback completed successfully!"
}

# Run main function
main "$@"
