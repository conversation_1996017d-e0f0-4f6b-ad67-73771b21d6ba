#!/bin/bash

# WordPress URL Update Script
# This script updates WordPress URLs in the database without WP-CLI
# Useful for containers that don't have WP-CLI installed

set -e  # Exit on any error

# Configuration variables
OLD_DOMAIN=""
NEW_DOMAIN=""
WORDPRESS_DIR="/opt/wordpress"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Get user input
get_user_input() {
    echo "=== WordPress URL Update ==="
    echo
    
    while [[ -z "$OLD_DOMAIN" ]]; do
        read -p "Enter current domain (e.g., olddomain.com): " OLD_DOMAIN
        if [[ ! "$OLD_DOMAIN" =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; then
            error "Invalid domain format. Please enter a valid domain."
            OLD_DOMAIN=""
        fi
    done
    
    while [[ -z "$NEW_DOMAIN" ]]; do
        read -p "Enter new domain (e.g., newdomain.com): " NEW_DOMAIN
        if [[ ! "$NEW_DOMAIN" =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; then
            error "Invalid domain format. Please enter a valid domain."
            NEW_DOMAIN=""
        elif [[ "$NEW_DOMAIN" == "$OLD_DOMAIN" ]]; then
            error "New domain cannot be the same as current domain."
            NEW_DOMAIN=""
        fi
    done
    
    echo
    info "URL Update Details:"
    echo "  From: $OLD_DOMAIN"
    echo "  To: $NEW_DOMAIN"
    echo
    read -p "Continue with URL update? (y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo "URL update cancelled."
        exit 0
    fi
}

# Update WordPress URLs in database
update_wordpress_urls() {
    log "Updating WordPress URLs in database..."
    
    cd "$WORDPRESS_DIR"
    
    # Check if containers are running
    if ! docker compose ps | grep -q "wordpress_db.*Up"; then
        error "WordPress database container is not running"
        exit 1
    fi
    
    # Get database credentials from .env file
    if [[ -f ".env" ]]; then
        source .env
    else
        error ".env file not found in $WORDPRESS_DIR"
        exit 1
    fi
    
    log "Updating WordPress home and siteurl options..."
    
    # Update WordPress URLs using direct MySQL commands
    docker compose exec -T db mysql -u root -p${MYSQL_ROOT_PASSWORD} ${MYSQL_DATABASE} -e "
        UPDATE wp_options SET option_value = 'https://$NEW_DOMAIN' WHERE option_name = 'home';
        UPDATE wp_options SET option_value = 'https://$NEW_DOMAIN' WHERE option_name = 'siteurl';
    "
    
    if [[ $? -eq 0 ]]; then
        log "WordPress home and siteurl updated successfully"
    else
        error "Failed to update WordPress URLs"
        return 1
    fi
    
    # Show what would be replaced in the database
    log "Checking for additional domain references in database..."
    REPLACE_COUNT=$(docker compose exec -T db mysql -u root -p${MYSQL_ROOT_PASSWORD} ${MYSQL_DATABASE} -e "
        SELECT COUNT(*) as count FROM (
            SELECT * FROM wp_posts WHERE post_content LIKE '%https://$OLD_DOMAIN%' OR post_content LIKE '%http://$OLD_DOMAIN%'
            UNION ALL
            SELECT * FROM wp_options WHERE option_value LIKE '%https://$OLD_DOMAIN%' OR option_value LIKE '%http://$OLD_DOMAIN%'
            UNION ALL
            SELECT * FROM wp_comments WHERE comment_content LIKE '%https://$OLD_DOMAIN%' OR comment_content LIKE '%http://$OLD_DOMAIN%'
        ) as combined;
    " | tail -n 1)
    
    if [[ "$REPLACE_COUNT" -gt 0 ]]; then
        warning "Found $REPLACE_COUNT additional references to old domain in database"
        echo "This includes content in posts, comments, and other options."
        read -p "Proceed with full database search and replace? (y/N): " db_confirm
        
        if [[ "$db_confirm" =~ ^[Yy]$ ]]; then
            log "Performing database search and replace..."
            
            # Replace in wp_posts
            docker compose exec -T db mysql -u root -p${MYSQL_ROOT_PASSWORD} ${MYSQL_DATABASE} -e "
                UPDATE wp_posts SET post_content = REPLACE(post_content, 'https://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
                UPDATE wp_posts SET post_content = REPLACE(post_content, 'http://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
                UPDATE wp_posts SET post_excerpt = REPLACE(post_excerpt, 'https://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
                UPDATE wp_posts SET post_excerpt = REPLACE(post_excerpt, 'http://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
            "
            
            # Replace in wp_options (excluding home and siteurl which we already updated)
            docker compose exec -T db mysql -u root -p${MYSQL_ROOT_PASSWORD} ${MYSQL_DATABASE} -e "
                UPDATE wp_options SET option_value = REPLACE(option_value, 'https://$OLD_DOMAIN', 'https://$NEW_DOMAIN') 
                WHERE option_name NOT IN ('home', 'siteurl');
                UPDATE wp_options SET option_value = REPLACE(option_value, 'http://$OLD_DOMAIN', 'https://$NEW_DOMAIN') 
                WHERE option_name NOT IN ('home', 'siteurl');
            "
            
            # Replace in wp_comments
            docker compose exec -T db mysql -u root -p${MYSQL_ROOT_PASSWORD} ${MYSQL_DATABASE} -e "
                UPDATE wp_comments SET comment_content = REPLACE(comment_content, 'https://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
                UPDATE wp_comments SET comment_content = REPLACE(comment_content, 'http://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
                UPDATE wp_comments SET comment_author_url = REPLACE(comment_author_url, 'https://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
                UPDATE wp_comments SET comment_author_url = REPLACE(comment_author_url, 'http://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
            "
            
            # Replace in wp_commentmeta
            docker compose exec -T db mysql -u root -p${MYSQL_ROOT_PASSWORD} ${MYSQL_DATABASE} -e "
                UPDATE wp_commentmeta SET meta_value = REPLACE(meta_value, 'https://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
                UPDATE wp_commentmeta SET meta_value = REPLACE(meta_value, 'http://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
            "
            
            # Replace in wp_postmeta
            docker compose exec -T db mysql -u root -p${MYSQL_ROOT_PASSWORD} ${MYSQL_DATABASE} -e "
                UPDATE wp_postmeta SET meta_value = REPLACE(meta_value, 'https://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
                UPDATE wp_postmeta SET meta_value = REPLACE(meta_value, 'http://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
            "
            
            log "Database search and replace completed"
        else
            warning "Full database search and replace skipped"
            info "You can manually update content later through WordPress admin"
        fi
    else
        log "No additional domain references found in database"
    fi
    
    echo
    echo "=========================================="
    echo "WordPress URL Update Complete!"
    echo "=========================================="
    echo
    info "Updated URLs:"
    echo "  - WordPress Home: https://$NEW_DOMAIN"
    echo "  - WordPress Site URL: https://$NEW_DOMAIN"
    echo
    info "Next Steps:"
    echo "  1. Clear any WordPress caching plugins"
    echo "  2. Test your website functionality"
    echo "  3. Update any hardcoded URLs in themes/plugins"
    echo "  4. Check WordPress admin settings"
    echo
}

# Main execution
main() {
    echo "Starting WordPress URL Update..."
    echo
    
    check_root
    get_user_input
    update_wordpress_urls
    
    log "URL update completed successfully!"
}

# Run main function
main "$@"
