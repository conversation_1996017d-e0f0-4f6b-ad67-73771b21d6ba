#!/bin/bash

# WordPress Docker Production Setup Script
# Compatible with Ubuntu 22.04+
# This script sets up WordPress with Dock<PERSON>, Nginx reverse proxy, and SSL certificates

set -e  # Exit on any error

# Configuration variables
DOMAIN=""
EMAIL=""
WORDPRESS_DIR="/opt/wordpress"
NGINX_CONF="/etc/nginx/sites-available/wordpress"
NGINX_ENABLED="/etc/nginx/sites-enabled/wordpress"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Get domain and email from user
get_user_input() {
    echo "=== WordPress Docker Production Setup ==="
    echo
    
    while [[ -z "$DOMAIN" ]]; do
        read -p "Enter your domain name (e.g., example.com): " DOMAIN
        if [[ ! "$DOMAIN" =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; then
            error "Invalid domain format. Please enter a valid domain."
            DOMAIN=""
        fi
    done
    
    while [[ -z "$EMAIL" ]]; do
        read -p "Enter your email address for SSL certificate: " EMAIL
        if [[ ! "$EMAIL" =~ ^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$ ]]; then
            error "Invalid email format. Please enter a valid email."
            EMAIL=""
        fi
    done
    
    echo
    info "Domain: $DOMAIN"
    info "Email: $EMAIL"
    echo
    read -p "Continue with setup? (y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo "Setup cancelled."
        exit 0
    fi
}

# Update system packages
update_system() {
    log "Updating system packages..."
    apt-get update -y
    apt-get upgrade -y
    log "System packages updated successfully"
}

# Install required dependencies
install_dependencies() {
    log "Installing required dependencies..."
    
    # Install basic packages
    apt-get install -y curl git ufw software-properties-common ca-certificates gnupg lsb-release
    
    # Add Docker's official GPG key
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # Add Docker repository
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # Update package index
    apt-get update -y
    
    # Install Docker and Docker Compose
    apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    
    # Install Nginx
    apt-get install -y nginx
    
    # Install Certbot
    apt-get install -y snapd
    snap install core; snap refresh core
    snap install --classic certbot
    ln -sf /snap/bin/certbot /usr/bin/certbot
    
    log "Dependencies installed successfully"
}

# Configure Docker
configure_docker() {
    log "Configuring Docker..."
    
    # Enable and start Docker
    systemctl enable docker
    systemctl start docker
    
    # Add current user to docker group if not root
    if [[ $SUDO_USER ]]; then
        usermod -aG docker $SUDO_USER
        info "Added $SUDO_USER to docker group (logout/login required)"
    fi
    
    log "Docker configured successfully"
}

# Create WordPress directory and Docker Compose configuration
create_wordpress_config() {
    log "Creating WordPress Docker configuration..."
    
    # Create WordPress directory
    mkdir -p $WORDPRESS_DIR
    cd $WORDPRESS_DIR
    
    # Create .env file
    cat > .env << EOF
# MySQL Configuration
MYSQL_ROOT_PASSWORD=rootpassword
MYSQL_DATABASE=wordpress
MYSQL_USER=wpuser
MYSQL_PASSWORD=wppassword

# WordPress Configuration
WORDPRESS_DB_HOST=db:3306
WORDPRESS_DB_NAME=wordpress
WORDPRESS_DB_USER=wpuser
WORDPRESS_DB_PASSWORD=wppassword

# WordPress Admin Configuration
WORDPRESS_ADMIN_USER=admin
WORDPRESS_ADMIN_PASSWORD=adminpass
WORDPRESS_ADMIN_EMAIL=<EMAIL>
EOF
    
    # Create docker-compose.yml
    cat > docker-compose.yml << EOF
version: '3.8'

services:
  db:
    image: mysql:8.0
    container_name: wordpress_db
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: \${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: \${MYSQL_DATABASE}
      MYSQL_USER: \${MYSQL_USER}
      MYSQL_PASSWORD: \${MYSQL_PASSWORD}
    volumes:
      - db_data:/var/lib/mysql
    networks:
      - wordpress_network
    command: --default-authentication-plugin=mysql_native_password

  wordpress:
    depends_on:
      - db
    image: wordpress:latest
    container_name: wordpress_app
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      WORDPRESS_DB_HOST: \${WORDPRESS_DB_HOST}
      WORDPRESS_DB_NAME: \${WORDPRESS_DB_NAME}
      WORDPRESS_DB_USER: \${WORDPRESS_DB_USER}
      WORDPRESS_DB_PASSWORD: \${WORDPRESS_DB_PASSWORD}
    volumes:
      - wordpress_data:/var/www/html
      - ./php-uploads.ini:/usr/local/etc/php/conf.d/uploads.ini
    networks:
      - wordpress_network

volumes:
  db_data:
  wordpress_data:

networks:
  wordpress_network:
    driver: bridge
EOF
    
    # Set proper permissions
    chmod 600 .env
    
    # Create PHP configuration for file uploads
    cat > php-uploads.ini << EOF
file_uploads = On
upload_max_filesize = 100M
post_max_size = 100M
max_execution_time = 300
max_input_time = 300
memory_limit = 256M
max_file_uploads = 20
EOF
    
    log "WordPress Docker configuration created"
}

# Start WordPress containers
start_wordpress() {
    log "Starting WordPress containers..."
    
    cd $WORDPRESS_DIR
    docker compose up -d
    
    # Wait for containers to be ready
    info "Waiting for WordPress to be ready..."
    sleep 30
    
    # Check if containers are running
    if docker compose ps | grep -q "Up"; then
        log "WordPress containers started successfully"
    else
        error "Failed to start WordPress containers"
        exit 1
    fi
}

# Configure Nginx reverse proxy
configure_nginx() {
    log "Configuring Nginx reverse proxy..."
    
    # Create Nginx configuration
    cat > $NGINX_CONF << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    
    # Redirect all HTTP requests to HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN www.$DOMAIN;
    
    # SSL Configuration (will be managed by Certbot)
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    
    # SSL Security Headers
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Proxy Configuration
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header X-Forwarded-Host \$host;
        proxy_set_header X-Forwarded-Port \$server_port;
        
        # Buffering
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Handle large file uploads
    client_max_body_size 100M;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
EOF
    
    # Create temporary HTTP-only configuration for Certbot
    cat > ${NGINX_CONF}.temp << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    client_max_body_size 100M;
}
EOF
    
    # Enable the temporary site
    ln -sf ${NGINX_CONF}.temp $NGINX_ENABLED
    
    # Test and reload Nginx
    nginx -t
    systemctl reload nginx
    
    log "Nginx reverse proxy configured"
}

# Configure firewall
configure_firewall() {
    log "Configuring UFW firewall..."
    
    # Reset UFW to default
    ufw --force reset
    
    # Set default policies
    ufw default deny incoming
    ufw default allow outgoing
    
    # Allow SSH (port 22)
    ufw allow 22/tcp
    
    # Allow HTTP and HTTPS
    ufw allow 80/tcp
    ufw allow 443/tcp
    
    # Enable UFW
    ufw --force enable
    
    log "Firewall configured successfully"
}

# Setup SSL certificate
setup_ssl() {
    log "Setting up SSL certificate with Let's Encrypt..."
    
    # Get SSL certificate
    certbot certonly --nginx --non-interactive --agree-tos --email $EMAIL -d $DOMAIN -d www.$DOMAIN
    
    if [[ $? -eq 0 ]]; then
        log "SSL certificate obtained successfully"
        
        # Replace temporary config with SSL config
        ln -sf $NGINX_CONF $NGINX_ENABLED
        
        # Test and reload Nginx
        nginx -t
        systemctl reload nginx
        
        # Setup automatic renewal
        (crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet --nginx") | crontab -
        
        log "SSL certificate configured and auto-renewal enabled"
    else
        error "Failed to obtain SSL certificate"
        warning "WordPress is still accessible via HTTP at http://$DOMAIN"
    fi
}

# Final checks and output
final_checks() {
    log "Performing final checks..."
    
    # Check if WordPress is accessible
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080 | grep -q "200\|301\|302"; then
        log "WordPress is responding correctly"
    else
        warning "WordPress might not be fully ready yet"
    fi
    
    # Check if Nginx is serving the site
    if curl -s -o /dev/null -w "%{http_code}" http://$DOMAIN | grep -q "200\|301\|302"; then
        log "Nginx reverse proxy is working"
    else
        warning "Nginx might not be serving the site correctly"
    fi
    
    # Output final information
    echo
    echo "=========================================="
    echo "WordPress Setup Complete!"
    echo "=========================================="
    echo
    info "Access URLs:"
    echo "  - HTTPS: https://$DOMAIN"
    echo "  - HTTP: http://$DOMAIN (redirects to HTTPS)"
    echo
    info "WordPress Admin Credentials:"
    echo "  - Admin URL: https://$DOMAIN/wp-admin"
    echo "  - Username: admin"
    echo "  - Password: adminpass"
    echo "  - Email: <EMAIL>"
    echo
    info "Database Credentials:"
    echo "  - MySQL Root Password: rootpassword"
    echo "  - WordPress Database: wordpress"
    echo "  - WordPress DB User: wpuser"
    echo "  - WordPress DB Password: wppassword"
    echo
    info "Important Notes:"
    echo "  - Change default passwords immediately after login"
    echo "  - WordPress files are stored in: $WORDPRESS_DIR"
    echo "  - SSL certificate auto-renewal is configured"
    echo "  - Firewall (UFW) is enabled with ports 22, 80, 443 open"
    echo "  - Docker containers will auto-restart unless stopped"
    echo
    warning "Security Recommendations:"
    echo "  - Change all default passwords"
    echo "  - Update WordPress regularly"
    echo "  - Configure WordPress security plugins"
    echo "  - Set up regular backups"
    echo "  - Review firewall rules periodically"
    echo
    log "Setup completed successfully!"
}

# Cleanup function
cleanup() {
    if [[ -f "${NGINX_CONF}.temp" ]]; then
        rm -f "${NGINX_CONF}.temp"
    fi
}

# Main execution
main() {
    echo "Starting WordPress Docker Production Setup..."
    echo
    
    check_root
    get_user_input
    
    # Set trap for cleanup
    trap cleanup EXIT
    
    update_system
    install_dependencies
    configure_docker
    create_wordpress_config
    start_wordpress
    configure_nginx
    configure_firewall
    setup_ssl
    final_checks
    
    cleanup
}

# Run main function
main "$@"