#!/bin/bash

# WordPress Domain Migration Script
# This script migrates a WordPress site to a new domain
# Compatible with the setup created by setup_wordpress_server.sh

set -e  # Exit on any error

# Configuration variables
OLD_DOMAIN=""
NEW_DOMAIN=""
EMAIL=""
WORDPRESS_DIR="/opt/wordpress"
NGINX_CONF="/etc/nginx/sites-enabled/wordpress"
NGINX_AVAILABLE="/etc/nginx/sites-available/wordpress"
BACKUP_DIR="/opt/wordpress_backup_$(date +%Y%m%d_%H%M%S)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Get current domain from Nginx config
get_current_domain() {
    if [[ -f "$NGINX_CONF" ]]; then
        OLD_DOMAIN=$(grep -oP 'server_name \K[^;]*' "$NGINX_CONF" | head -1 | awk '{print $1}')
        if [[ -n "$OLD_DOMAIN" ]]; then
            info "Current domain detected: $OLD_DOMAIN"
        else
            error "Could not detect current domain from Nginx configuration"
            exit 1
        fi
    else
        error "Nginx configuration file not found at $NGINX_CONF"
        exit 1
    fi
}

# Get user input for new domain
get_user_input() {
    echo "=== WordPress Domain Migration ==="
    echo
    
    info "Current domain: $OLD_DOMAIN"
    echo
    
    while [[ -z "$NEW_DOMAIN" ]]; do
        read -p "Enter new domain name (e.g., newdomain.com): " NEW_DOMAIN
        if [[ ! "$NEW_DOMAIN" =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; then
            error "Invalid domain format. Please enter a valid domain."
            NEW_DOMAIN=""
        elif [[ "$NEW_DOMAIN" == "$OLD_DOMAIN" ]]; then
            error "New domain cannot be the same as current domain."
            NEW_DOMAIN=""
        fi
    done
    
    while [[ -z "$EMAIL" ]]; do
        read -p "Enter your email address for SSL certificate: " EMAIL
        if [[ ! "$EMAIL" =~ ^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$ ]]; then
            error "Invalid email format. Please enter a valid email."
            EMAIL=""
        fi
    done
    
    echo
    info "Migration Details:"
    echo "  From: $OLD_DOMAIN"
    echo "  To: $NEW_DOMAIN"
    echo "  Email: $EMAIL"
    echo "  Backup will be created at: $BACKUP_DIR"
    echo
    warning "IMPORTANT: Make sure $NEW_DOMAIN is pointing to this server's IP address!"
    echo
    read -p "Continue with domain migration? (y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo "Migration cancelled."
        exit 0
    fi
}

# Check if Python routing service is running
check_routing_service() {
    log "Checking Python routing service..."

    if curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:3000/route | grep -q "200\|404\|405"; then
        log "Python routing service is responding on port 3000"
    else
        warning "Python routing service on port 3000 is not responding"
        echo "Your Nginx configuration depends on a Python service running on port 3000"
        echo "Make sure this service is running before proceeding."
        read -p "Continue anyway? (y/N): " service_confirm
        if [[ ! "$service_confirm" =~ ^[Yy]$ ]]; then
            error "Please start the Python routing service and try again"
            exit 1
        fi
    fi
}

# Verify DNS resolution
verify_dns() {
    log "Verifying DNS resolution for $NEW_DOMAIN..."
    
    # Get server's public IP
    SERVER_IP=$(curl -s ifconfig.me || curl -s ipinfo.io/ip || curl -s icanhazip.com)
    
    if [[ -z "$SERVER_IP" ]]; then
        warning "Could not determine server's public IP address"
        read -p "Enter your server's public IP address: " SERVER_IP
    fi
    
    info "Server IP: $SERVER_IP"
    
    # Check DNS resolution
    RESOLVED_IP=$(dig +short $NEW_DOMAIN | tail -1)
    
    if [[ "$RESOLVED_IP" == "$SERVER_IP" ]]; then
        log "DNS resolution verified successfully"
    else
        warning "DNS resolution mismatch!"
        echo "  Expected: $SERVER_IP"
        echo "  Resolved: $RESOLVED_IP"
        echo
        read -p "Continue anyway? DNS propagation might still be in progress (y/N): " dns_confirm
        if [[ ! "$dns_confirm" =~ ^[Yy]$ ]]; then
            error "Please update your DNS records and try again"
            exit 1
        fi
    fi
}

# Create backup
create_backup() {
    log "Creating backup before migration..."
    
    mkdir -p "$BACKUP_DIR"
    
    # Backup WordPress files
    if [[ -d "$WORDPRESS_DIR" ]]; then
        cp -r "$WORDPRESS_DIR" "$BACKUP_DIR/wordpress_files"
        log "WordPress files backed up"
    fi
    
    # Backup Nginx configuration
    if [[ -f "$NGINX_CONF" ]]; then
        cp "$NGINX_CONF" "$BACKUP_DIR/nginx_wordpress.conf"
        log "Nginx configuration backed up"
    fi
    
    # Backup WordPress database
    cd "$WORDPRESS_DIR"
    if docker compose ps | grep -q "wordpress_db.*Up"; then
        docker compose exec -T db mysqldump -u root -prootpassword wordpress > "$BACKUP_DIR/wordpress_database.sql"
        log "WordPress database backed up"
    else
        warning "WordPress database container not running, skipping database backup"
    fi
    
    log "Backup completed at: $BACKUP_DIR"
}

# Update WordPress URLs in database
update_wordpress_urls() {
    log "Updating WordPress URLs in database..."

    cd "$WORDPRESS_DIR"

    # Check if containers are running
    if ! docker compose ps | grep -q "wordpress_db.*Up"; then
        error "WordPress database container is not running"
        exit 1
    fi

    # Get database credentials from .env file
    if [[ -f ".env" ]]; then
        source .env
    else
        error ".env file not found in $WORDPRESS_DIR"
        exit 1
    fi

    log "Updating WordPress home and siteurl options..."

    # Update WordPress URLs using direct MySQL commands
    docker compose exec -T db mysql -u root -p${MYSQL_ROOT_PASSWORD} ${MYSQL_DATABASE} -e "
        UPDATE wp_options SET option_value = 'https://$NEW_DOMAIN' WHERE option_name = 'home';
        UPDATE wp_options SET option_value = 'https://$NEW_DOMAIN' WHERE option_name = 'siteurl';
    "

    if [[ $? -eq 0 ]]; then
        log "WordPress home and siteurl updated successfully"
    else
        error "Failed to update WordPress URLs"
        return 1
    fi

    # Show what would be replaced in the database
    log "Checking for additional domain references in database..."
    REPLACE_COUNT=$(docker compose exec -T db mysql -u root -p${MYSQL_ROOT_PASSWORD} ${MYSQL_DATABASE} -e "
        SELECT COUNT(*) as count FROM (
            SELECT * FROM wp_posts WHERE post_content LIKE '%https://$OLD_DOMAIN%' OR post_content LIKE '%http://$OLD_DOMAIN%'
            UNION ALL
            SELECT * FROM wp_options WHERE option_value LIKE '%https://$OLD_DOMAIN%' OR option_value LIKE '%http://$OLD_DOMAIN%'
            UNION ALL
            SELECT * FROM wp_comments WHERE comment_content LIKE '%https://$OLD_DOMAIN%' OR comment_content LIKE '%http://$OLD_DOMAIN%'
        ) as combined;
    " | tail -n 1)

    if [[ "$REPLACE_COUNT" -gt 0 ]]; then
        warning "Found $REPLACE_COUNT additional references to old domain in database"
        echo "This includes content in posts, comments, and other options."
        read -p "Proceed with full database search and replace? (y/N): " db_confirm

        if [[ "$db_confirm" =~ ^[Yy]$ ]]; then
            log "Performing database search and replace..."

            # Replace in wp_posts
            docker compose exec -T db mysql -u root -p${MYSQL_ROOT_PASSWORD} ${MYSQL_DATABASE} -e "
                UPDATE wp_posts SET post_content = REPLACE(post_content, 'https://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
                UPDATE wp_posts SET post_content = REPLACE(post_content, 'http://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
                UPDATE wp_posts SET post_excerpt = REPLACE(post_excerpt, 'https://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
                UPDATE wp_posts SET post_excerpt = REPLACE(post_excerpt, 'http://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
            "

            # Replace in wp_options (excluding home and siteurl which we already updated)
            docker compose exec -T db mysql -u root -p${MYSQL_ROOT_PASSWORD} ${MYSQL_DATABASE} -e "
                UPDATE wp_options SET option_value = REPLACE(option_value, 'https://$OLD_DOMAIN', 'https://$NEW_DOMAIN')
                WHERE option_name NOT IN ('home', 'siteurl');
                UPDATE wp_options SET option_value = REPLACE(option_value, 'http://$OLD_DOMAIN', 'https://$NEW_DOMAIN')
                WHERE option_name NOT IN ('home', 'siteurl');
            "

            # Replace in wp_comments
            docker compose exec -T db mysql -u root -p${MYSQL_ROOT_PASSWORD} ${MYSQL_DATABASE} -e "
                UPDATE wp_comments SET comment_content = REPLACE(comment_content, 'https://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
                UPDATE wp_comments SET comment_content = REPLACE(comment_content, 'http://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
                UPDATE wp_comments SET comment_author_url = REPLACE(comment_author_url, 'https://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
                UPDATE wp_comments SET comment_author_url = REPLACE(comment_author_url, 'http://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
            "

            # Replace in wp_commentmeta
            docker compose exec -T db mysql -u root -p${MYSQL_ROOT_PASSWORD} ${MYSQL_DATABASE} -e "
                UPDATE wp_commentmeta SET meta_value = REPLACE(meta_value, 'https://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
                UPDATE wp_commentmeta SET meta_value = REPLACE(meta_value, 'http://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
            "

            # Replace in wp_postmeta
            docker compose exec -T db mysql -u root -p${MYSQL_ROOT_PASSWORD} ${MYSQL_DATABASE} -e "
                UPDATE wp_postmeta SET meta_value = REPLACE(meta_value, 'https://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
                UPDATE wp_postmeta SET meta_value = REPLACE(meta_value, 'http://$OLD_DOMAIN', 'https://$NEW_DOMAIN');
            "

            log "Database search and replace completed"
        else
            warning "Full database search and replace skipped"
            info "You can manually update content later through WordPress admin"
        fi
    else
        log "No additional domain references found in database"
    fi
}

# Update Nginx configuration
update_nginx_config() {
    log "Updating Nginx configuration for new domain..."

    # Create new Nginx configuration with new domain and preserve dynamic routing
    cat > $NGINX_AVAILABLE << EOF
server {
    listen 80;
    server_name $NEW_DOMAIN www.$NEW_DOMAIN;

    # Redirect all HTTP requests to HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $NEW_DOMAIN www.$NEW_DOMAIN;

    # Add resolver for dynamic proxy_pass
    resolver 127.0.0.1 valid=30s;

    # Internal location for Python routing service
    location = /auth-route {
        internal;
        proxy_pass http://127.0.0.1:3000/route;
        proxy_pass_request_body off;
        proxy_set_header Content-Length "";
        proxy_set_header X-Original-URI \$request_uri;
        proxy_set_header X-User-Agent \$http_user_agent;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_connect_timeout 2s;
        proxy_read_timeout 2s;
    }


    # SSL Configuration (will be managed by Certbot)
    ssl_certificate /etc/letsencrypt/live/$NEW_DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$NEW_DOMAIN/privkey.pem;

    # SSL Security Headers
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Proxy Configuration with Dynamic Routing
    location / {
        # Call the Python service to determine routing
        auth_request /auth-route;
        auth_request_set \$proxy_port \$upstream_http_x_pro_po;
        auth_request_set \$bot_type \$upstream_http_x_bot_type;

        # Set port logic - use proxy_port if available, fallback to 8080
        set \$final_port \$bot_type;
        if (\$bot_type = "") {
            set \$final_port "8080";
        }

        proxy_pass http://127.0.0.1:\$final_port;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header X-Forwarded-Host \$host;
        proxy_set_header X-Forwarded-Port \$server_port;

        # Buffering
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;

        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        # Debug headers (commented out)
        #add_header X-Routed-Port \$final_port always;
        #add_header X-Debug-Proxy-Port \$proxy_port always;
        #add_header X-Debug-Bot-Type \$bot_type always;
        #add_header X-Debug-Raw-Port \$upstream_http_x_pro_po always;
    }

    # Handle large file uploads
    client_max_body_size 100M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
EOF

    # Create temporary HTTP-only configuration for SSL setup with dynamic routing
    cat > ${NGINX_AVAILABLE}.temp << EOF
server {
    listen 80;
    server_name $NEW_DOMAIN www.$NEW_DOMAIN;

    # Add resolver for dynamic proxy_pass
    resolver 127.0.0.1 valid=30s;

    # Internal location for Python routing service
    location = /auth-route {
        internal;
        proxy_pass http://127.0.0.1:3000/route;
        proxy_pass_request_body off;
        proxy_set_header Content-Length "";
        proxy_set_header X-Original-URI \$request_uri;
        proxy_set_header X-User-Agent \$http_user_agent;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_connect_timeout 2s;
        proxy_read_timeout 2s;
    }

    location / {
        # Call the Python service to determine routing
        auth_request /auth-route;
        auth_request_set \$proxy_port \$upstream_http_x_pro_po;
        auth_request_set \$bot_type \$upstream_http_x_bot_type;

        # Set port logic - use proxy_port if available, fallback to 8080
        set \$final_port \$bot_type;
        if (\$bot_type = "") {
            set \$final_port "8080";
        }

        proxy_pass http://127.0.0.1:\$final_port;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    client_max_body_size 100M;
}
EOF

    # Enable the temporary configuration
    ln -sf ${NGINX_AVAILABLE}.temp $NGINX_CONF

    # Test and reload Nginx
    nginx -t
    systemctl reload nginx

    log "Nginx configuration updated for new domain"
}

# Setup SSL certificate for new domain
setup_new_ssl() {
    log "Setting up SSL certificate for new domain..."

    # Get SSL certificate for new domain
    certbot certonly --nginx --non-interactive --agree-tos --email $EMAIL -d $NEW_DOMAIN -d www.$NEW_DOMAIN

    if [[ $? -eq 0 ]]; then
        log "SSL certificate obtained successfully for $NEW_DOMAIN"

        # Replace temporary config with SSL config
        ln -sf $NGINX_AVAILABLE $NGINX_CONF

        # Test and reload Nginx
        nginx -t
        systemctl reload nginx

        log "SSL certificate configured for new domain"
    else
        error "Failed to obtain SSL certificate for $NEW_DOMAIN"
        warning "Site is accessible via HTTP at http://$NEW_DOMAIN"
        return 1
    fi
}

# Clean up old SSL certificates (optional)
cleanup_old_ssl() {
    log "Cleaning up old SSL certificates..."

    read -p "Remove SSL certificates for old domain $OLD_DOMAIN? (y/N): " cleanup_confirm
    if [[ "$cleanup_confirm" =~ ^[Yy]$ ]]; then
        certbot delete --cert-name $OLD_DOMAIN
        log "Old SSL certificates removed"
    else
        info "Old SSL certificates kept (you can remove them manually later with: certbot delete --cert-name $OLD_DOMAIN)"
    fi
}

# Final checks and verification
final_checks() {
    log "Performing final checks..."

    # Check if WordPress containers are running
    cd "$WORDPRESS_DIR"
    if docker compose ps | grep -q "Up"; then
        log "WordPress containers are running"
    else
        warning "Some WordPress containers might not be running"
    fi

    # Check if WordPress is accessible via new domain
    sleep 10  # Wait for services to stabilize

    if curl -s -o /dev/null -w "%{http_code}" http://$NEW_DOMAIN | grep -q "200\|301\|302"; then
        log "New domain is responding correctly"
    else
        warning "New domain might not be fully ready yet"
    fi

    # Check HTTPS if SSL was set up
    if [[ -f "/etc/letsencrypt/live/$NEW_DOMAIN/fullchain.pem" ]]; then
        if curl -s -o /dev/null -w "%{http_code}" https://$NEW_DOMAIN | grep -q "200\|301\|302"; then
            log "HTTPS is working for new domain"
        else
            warning "HTTPS might not be working correctly"
        fi
    fi

    # Output migration summary
    echo
    echo "=========================================="
    echo "WordPress Domain Migration Complete!"
    echo "=========================================="
    echo
    info "Migration Summary:"
    echo "  - Old Domain: $OLD_DOMAIN"
    echo "  - New Domain: $NEW_DOMAIN"
    echo "  - Backup Location: $BACKUP_DIR"
    echo
    info "Access URLs:"
    echo "  - HTTPS: https://$NEW_DOMAIN"
    echo "  - HTTP: http://$NEW_DOMAIN (redirects to HTTPS)"
    echo "  - Admin: https://$NEW_DOMAIN/wp-admin"
    echo
    info "Next Steps:"
    echo "  1. Test your website thoroughly"
    echo "  2. Update any external services pointing to old domain"
    echo "  3. Set up redirects from old domain if needed"
    echo "  4. Update DNS records if not done already"
    echo "  5. Clear any caching plugins in WordPress"
    echo
    warning "Important Notes:"
    echo "  - Backup is available at: $BACKUP_DIR"
    echo "  - Old SSL certificates are still available"
    echo "  - Test all functionality before removing backups"
    echo
}

# Rollback function
rollback() {
    error "Rolling back changes..."

    if [[ -d "$BACKUP_DIR" ]]; then
        # Restore Nginx configuration
        if [[ -f "$BACKUP_DIR/nginx_wordpress.conf" ]]; then
            cp "$BACKUP_DIR/nginx_wordpress.conf" "$NGINX_AVAILABLE"
            ln -sf "$NGINX_AVAILABLE" "$NGINX_CONF"
            nginx -t && systemctl reload nginx
            log "Nginx configuration restored"
        fi

        # Restore database if backup exists
        if [[ -f "$BACKUP_DIR/wordpress_database.sql" ]]; then
            cd "$WORDPRESS_DIR"
            docker compose exec -T db mysql -u root -prootpassword wordpress < "$BACKUP_DIR/wordpress_database.sql"
            log "Database restored from backup"
        fi

        log "Rollback completed. Your site should be accessible at the original domain."
    else
        error "Backup directory not found. Manual rollback required."
    fi
}

# Cleanup function
cleanup() {
    if [[ -f "${NGINX_AVAILABLE}.temp" ]]; then
        rm -f "${NGINX_AVAILABLE}.temp"
    fi
}

# Main execution
main() {
    echo "Starting WordPress Domain Migration..."
    echo

    check_root
    get_current_domain
    get_user_input

    # Set trap for cleanup and rollback on error
    trap cleanup EXIT
    trap rollback ERR

    check_routing_service
    verify_dns
    create_backup
    update_nginx_config
    setup_new_ssl
    update_wordpress_urls
    cleanup_old_ssl
    final_checks

    cleanup

    log "Domain migration completed successfully!"
}

# Run main function
main "$@"
