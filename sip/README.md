# SIP Server Setup and Management Scripts

This directory contains comprehensive scripts for setting up and managing a complete SIP server using Asterisk with full domain support.

## Scripts Overview

### 1. `setup_sip_server.sh` - Main SIP Server Setup
Sets up a complete Asterisk-based SIP server with security, SSL, and domain support.

### 2. `add_sip_user.sh` - User Management
Adds new SIP users with extensions and voicemail support.

## Quick Start

### Step 1: Setup SIP Server
```bash
sudo chmod +x setup_sip_server.sh
sudo ./setup_sip_server.sh <server_ip> <admin_username> <admin_password> <domain>
```

**Example:**
```bash
sudo ./setup_sip_server.sh ************** root pvt@1234helloworld oliviqo.com
```

### Step 2: Add SIP Users
```bash
sudo chmod +x add_sip_user.sh
sudo ./add_sip_user.sh <server_ip> <admin_username> <admin_password> <domain> <sip_username> <sip_password> [extension] [email]
```

**Example:**
```bash
sudo ./add_sip_user.sh ************* admin mypassword sip.example.com john johnpass 1001 <EMAIL>
```

## Features

### SIP Server Setup Features
- ✅ Complete Asterisk installation and configuration
- ✅ Domain-based SIP setup
- ✅ Firewall configuration (UFW)
- ✅ SSL/TLS support for secure SIP
- ✅ Fail2ban integration for security
- ✅ RTP media support (ports 10000-20000)
- ✅ Multiple codec support (ulaw, alaw, gsm, g726, g722, speex)
- ✅ NAT traversal support
- ✅ Voicemail system
- ✅ Management scripts

### User Management Features
- ✅ Automatic extension assignment
- ✅ Voicemail box creation
- ✅ Email notifications (optional)
- ✅ Configuration backup before changes
- ✅ Automatic Asterisk reload
- ✅ User verification
- ✅ SIP client configuration details

## Network Requirements

### Firewall Ports
The setup script automatically configures these ports:
- **5060/UDP & TCP**: SIP signaling
- **5061/UDP & TCP**: SIP TLS (secure)
- **10000-20000/UDP**: RTP media streams
- **4569/UDP**: IAX2 (Inter-Asterisk eXchange)
- **80/TCP & 443/TCP**: HTTP/HTTPS (for web management)
- **22/TCP**: SSH (maintained)

### Network Configuration
- Supports NAT environments
- Automatic external IP detection
- Local network ranges configured for:
  - ***********/16
  - 10.0.0.0/8
  - **********/12

## SIP Client Configuration

After adding a user, you'll receive configuration details like:

```
=== SIP Client Configuration ===
SIP Server: *************
Domain: sip.example.com
Username: john
Password: johnpass
Port: 5060 (UDP/TCP)
```

### Recommended SIP Clients
- **Desktop**: X-Lite, Zoiper, SoftPhone
- **Mobile**: Zoiper, Linphone, CSipSimple
- **Hardware**: Any SIP-compatible IP phone

## Built-in Test Features

### Test Extensions
- **\*43**: Echo test - speaks back what you say
- **\*60**: Speaking clock - announces current time
- **\*97**: Voicemail access from your extension

### Extension Dialing
- Internal extensions: Dial 1XXX (e.g., 1001, 1002)
- Voicemail: Automatically available for all users

## Management Tools

After setup, management scripts are available in `/opt/sip-management/`:

### Check Server Status
```bash
sudo /opt/sip-management/sip-status.sh
```

### List All Users
```bash
sudo /opt/sip-management/list-users.sh
```

### Remove a User
```bash
sudo /opt/sip-management/remove-user.sh <username_or_extension>
```

### Access Asterisk CLI
```bash
sudo asterisk -r
```

## Security Features

### Fail2ban Protection
- Automatic IP blocking after failed authentication attempts
- Configurable ban times and retry limits
- Monitors Asterisk logs for suspicious activity

### Authentication
- Strong password requirements recommended
- No guest access allowed
- Always authenticate reject for security

### SSL/TLS Support
- Self-signed certificates generated automatically
- Secure SIP (SIPS) support on port 5061
- Certificate location: `/etc/asterisk/keys/`

## Troubleshooting

### Check Asterisk Status
```bash
sudo systemctl status asterisk
```

### View Asterisk Logs
```bash
sudo tail -f /var/log/asterisk/messages
```

### Test SIP Registration
```bash
sudo asterisk -rx "sip show peers"
```

### Check Active Calls
```bash
sudo asterisk -rx "core show channels"
```

### Restart Services
```bash
sudo systemctl restart asterisk
sudo systemctl restart fail2ban
```

## File Locations

### Configuration Files
- Main SIP config: `/etc/asterisk/sip.conf`
- Extensions: `/etc/asterisk/extensions.conf`
- Voicemail: `/etc/asterisk/voicemail.conf`
- Modules: `/etc/asterisk/modules.conf`

### Backup Location
- Automatic backups: `/etc/asterisk/backup_YYYYMMDD_HHMMSS/`

### Voicemail Storage
- User voicemail: `/var/spool/asterisk/voicemail/default/EXTENSION/`

### SSL Certificates
- Certificate: `/etc/asterisk/keys/asterisk.pem`
- Private key: `/etc/asterisk/keys/asterisk.key`

## Advanced Configuration

### Adding Outbound Routes
Edit `/etc/asterisk/extensions.conf` and add routes in the `[outbound]` context.

### Custom Codecs
Modify the codec settings in `/etc/asterisk/sip.conf` under the `[general]` section.

### Database Integration
The system supports MySQL integration for user management and CDR (Call Detail Records).

## Support and Maintenance

### Regular Maintenance
1. Monitor disk space for voicemail storage
2. Review fail2ban logs for security events
3. Update system packages regularly
4. Backup configuration files before major changes

### Log Rotation
Asterisk logs are automatically rotated by the system logrotate service.

## Requirements

### System Requirements
- Ubuntu/Debian-based Linux distribution
- Root access
- Minimum 1GB RAM
- 10GB available disk space
- Static IP address or DDNS setup

### Network Requirements
- Open firewall ports as listed above
- Proper NAT configuration if behind router
- Domain name pointing to server IP (recommended)

---

**Note**: Always test in a development environment before deploying to production. Keep backups of your configuration files.
