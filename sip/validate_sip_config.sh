#!/bin/bash

# SIP Configuration Validation Script
# This script validates the SIP server configuration and performs health checks
# Usage: ./validate_sip_config.sh

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[CHECK]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Check if script is run as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root"
        exit 1
    fi
}

# Check if Asterisk is installed
check_asterisk_installation() {
    print_header "Checking Asterisk installation..."
    
    if command -v asterisk >/dev/null 2>&1; then
        ASTERISK_VERSION=$(asterisk -V 2>/dev/null | head -1)
        print_status "Asterisk is installed: $ASTERISK_VERSION"
    else
        print_error "Asterisk is not installed"
        return 1
    fi
}

# Check if Asterisk is running
check_asterisk_status() {
    print_header "Checking Asterisk service status..."
    
    if systemctl is-active --quiet asterisk; then
        print_status "Asterisk service is running"
        
        # Check if Asterisk is responding
        if asterisk -rx "core show version" >/dev/null 2>&1; then
            print_status "Asterisk is responding to commands"
        else
            print_warning "Asterisk service is running but not responding"
        fi
    else
        print_error "Asterisk service is not running"
        print_info "Try: systemctl start asterisk"
        return 1
    fi
}

# Check configuration files
check_config_files() {
    print_header "Checking configuration files..."
    
    local config_files=(
        "/etc/asterisk/sip.conf"
        "/etc/asterisk/extensions.conf"
        "/etc/asterisk/voicemail.conf"
        "/etc/asterisk/modules.conf"
    )
    
    for config_file in "${config_files[@]}"; do
        if [ -f "$config_file" ]; then
            print_status "Configuration file exists: $config_file"
            
            # Check if file is readable
            if [ -r "$config_file" ]; then
                print_status "Configuration file is readable: $config_file"
            else
                print_warning "Configuration file is not readable: $config_file"
            fi
        else
            print_error "Configuration file missing: $config_file"
        fi
    done
}

# Check SIP configuration
check_sip_config() {
    print_header "Checking SIP configuration..."
    
    if [ -f "/etc/asterisk/sip.conf" ]; then
        # Check for basic SIP settings
        if grep -q "udpbindaddr" /etc/asterisk/sip.conf; then
            print_status "SIP UDP binding configured"
        else
            print_warning "SIP UDP binding not found in configuration"
        fi
        
        if grep -q "domain=" /etc/asterisk/sip.conf; then
            DOMAIN=$(grep "domain=" /etc/asterisk/sip.conf | head -1 | cut -d'=' -f2)
            print_status "SIP domain configured: $DOMAIN"
        else
            print_warning "SIP domain not configured"
        fi
        
        if grep -q "externip=" /etc/asterisk/sip.conf; then
            EXTERN_IP=$(grep "externip=" /etc/asterisk/sip.conf | head -1 | cut -d'=' -f2)
            print_status "External IP configured: $EXTERN_IP"
        else
            print_warning "External IP not configured"
        fi
    fi
}

# Check SIP peers
check_sip_peers() {
    print_header "Checking SIP peers..."
    
    if systemctl is-active --quiet asterisk; then
        PEER_COUNT=$(asterisk -rx "sip show peers" 2>/dev/null | grep -c "^[0-9a-zA-Z]" || echo "0")
        if [ "$PEER_COUNT" -gt 0 ]; then
            print_status "SIP peers configured: $PEER_COUNT"
            print_info "Active SIP peers:"
            asterisk -rx "sip show peers" 2>/dev/null | grep "^[0-9a-zA-Z]" | head -5
        else
            print_warning "No SIP peers configured"
        fi
    fi
}

# Check network ports
check_network_ports() {
    print_header "Checking network ports..."
    
    local ports=("5060" "5061")
    
    for port in "${ports[@]}"; do
        if netstat -ln 2>/dev/null | grep -q ":$port "; then
            print_status "Port $port is listening"
        else
            print_warning "Port $port is not listening"
        fi
    done
    
    # Check RTP port range
    if netstat -ln 2>/dev/null | grep -q ":1000[0-9] "; then
        print_status "RTP ports appear to be configured"
    else
        print_warning "RTP ports may not be properly configured"
    fi
}

# Check firewall status
check_firewall() {
    print_header "Checking firewall configuration..."
    
    if command -v ufw >/dev/null 2>&1; then
        if ufw status | grep -q "Status: active"; then
            print_status "UFW firewall is active"
            
            # Check if SIP ports are allowed
            if ufw status | grep -q "5060"; then
                print_status "SIP port 5060 is allowed in firewall"
            else
                print_warning "SIP port 5060 may not be allowed in firewall"
            fi
        else
            print_warning "UFW firewall is not active"
        fi
    else
        print_info "UFW not installed, checking iptables..."
        if command -v iptables >/dev/null 2>&1; then
            print_info "iptables is available for firewall management"
        else
            print_warning "No firewall management tools found"
        fi
    fi
}

# Check fail2ban status
check_fail2ban() {
    print_header "Checking fail2ban status..."
    
    if command -v fail2ban-client >/dev/null 2>&1; then
        if systemctl is-active --quiet fail2ban; then
            print_status "fail2ban service is running"
            
            # Check if asterisk jail is active
            if fail2ban-client status 2>/dev/null | grep -q "asterisk"; then
                print_status "Asterisk fail2ban jails are active"
            else
                print_warning "Asterisk fail2ban jails may not be configured"
            fi
        else
            print_warning "fail2ban service is not running"
        fi
    else
        print_warning "fail2ban is not installed"
    fi
}

# Check SSL certificates
check_ssl_certificates() {
    print_header "Checking SSL certificates..."
    
    local cert_dir="/etc/asterisk/keys"
    local cert_file="$cert_dir/asterisk.pem"
    local key_file="$cert_dir/asterisk.key"
    
    if [ -f "$cert_file" ]; then
        print_status "SSL certificate found: $cert_file"
        
        # Check certificate expiration
        if openssl x509 -in "$cert_file" -noout -checkend 86400 >/dev/null 2>&1; then
            print_status "SSL certificate is valid"
        else
            print_warning "SSL certificate is expired or will expire soon"
        fi
    else
        print_warning "SSL certificate not found: $cert_file"
    fi
    
    if [ -f "$key_file" ]; then
        print_status "SSL private key found: $key_file"
    else
        print_warning "SSL private key not found: $key_file"
    fi
}

# Check voicemail configuration
check_voicemail() {
    print_header "Checking voicemail configuration..."
    
    if [ -f "/etc/asterisk/voicemail.conf" ]; then
        print_status "Voicemail configuration file exists"
        
        # Check for voicemail boxes
        VOICEMAIL_COUNT=$(grep -c "^[0-9]" /etc/asterisk/voicemail.conf 2>/dev/null || echo "0")
        if [ "$VOICEMAIL_COUNT" -gt 0 ]; then
            print_status "Voicemail boxes configured: $VOICEMAIL_COUNT"
        else
            print_warning "No voicemail boxes configured"
        fi
        
        # Check voicemail directory
        if [ -d "/var/spool/asterisk/voicemail" ]; then
            print_status "Voicemail spool directory exists"
        else
            print_warning "Voicemail spool directory not found"
        fi
    else
        print_error "Voicemail configuration file not found"
    fi
}

# Check system resources
check_system_resources() {
    print_header "Checking system resources..."
    
    # Check disk space
    DISK_USAGE=$(df /var/spool/asterisk 2>/dev/null | tail -1 | awk '{print $5}' | sed 's/%//' || echo "0")
    if [ "$DISK_USAGE" -lt 80 ]; then
        print_status "Disk space usage: ${DISK_USAGE}%"
    else
        print_warning "Disk space usage is high: ${DISK_USAGE}%"
    fi
    
    # Check memory usage
    MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    if [ "$MEMORY_USAGE" -lt 80 ]; then
        print_status "Memory usage: ${MEMORY_USAGE}%"
    else
        print_warning "Memory usage is high: ${MEMORY_USAGE}%"
    fi
}

# Generate summary report
generate_summary() {
    print_header "Validation Summary"
    
    echo ""
    echo "=== SIP Server Health Check Complete ==="
    echo ""
    
    if systemctl is-active --quiet asterisk; then
        echo "✓ Asterisk service is running"
    else
        echo "✗ Asterisk service is not running"
    fi
    
    if [ -f "/etc/asterisk/sip.conf" ]; then
        echo "✓ SIP configuration exists"
    else
        echo "✗ SIP configuration missing"
    fi
    
    PEER_COUNT=$(asterisk -rx "sip show peers" 2>/dev/null | grep -c "^[0-9a-zA-Z]" || echo "0")
    echo "ℹ SIP peers configured: $PEER_COUNT"
    
    VOICEMAIL_COUNT=$(grep -c "^[0-9]" /etc/asterisk/voicemail.conf 2>/dev/null || echo "0")
    echo "ℹ Voicemail boxes: $VOICEMAIL_COUNT"
    
    echo ""
    echo "For detailed management, use scripts in /opt/sip-management/"
    echo "Access Asterisk CLI with: asterisk -r"
}

# Main execution function
main() {
    print_header "Starting SIP Configuration Validation"
    echo ""
    
    check_root
    check_asterisk_installation
    check_asterisk_status
    check_config_files
    check_sip_config
    check_sip_peers
    check_network_ports
    check_firewall
    check_fail2ban
    check_ssl_certificates
    check_voicemail
    check_system_resources
    
    echo ""
    generate_summary
}

# Run main function
main "$@"
