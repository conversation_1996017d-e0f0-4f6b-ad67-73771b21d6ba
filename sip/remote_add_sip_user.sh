#!/bin/bash

# Remote SIP User Management Script using SSH
# This script adds new SIP users to the remote Asterisk server using sshpass
# Usage: ./remote_add_sip_user.sh <server_ip> <admin_username> <admin_password> <domain> <sip_username> <sip_password> [extension] [email]

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[REMOTE USER SETUP]${NC} $1"
}

# Validate input parameters
validate_params() {
    if [ $# -lt 6 ] || [ $# -gt 8 ]; then
        print_error "Usage: $0 <server_ip> <admin_username> <admin_password> <domain> <sip_username> <sip_password> [extension] [email]"
        print_error "Example: $0 ************** root 'pvt@1234helloworld' oliviqo.com john johnpass 1001 <EMAIL>"
        exit 1
    fi

    SERVER_IP="$1"
    ADMIN_USERNAME="$2"
    ADMIN_PASSWORD="$3"
    DOMAIN="$4"
    SIP_USERNAME="$5"
    SIP_PASSWORD="$6"
    EXTENSION="${7:-}"
    EMAIL="${8:-}"

    # Auto-generate extension if not provided
    if [ -z "$EXTENSION" ]; then
        EXTENSION=1001
        print_status "Auto-generating extension: $EXTENSION"
    fi

    # Validate parameters
    if ! [[ $SERVER_IP =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        print_error "Invalid IP address format: $SERVER_IP"
        exit 1
    fi

    if ! [[ $DOMAIN =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; then
        print_error "Invalid domain format: $DOMAIN"
        exit 1
    fi

    if ! [[ $EXTENSION =~ ^[0-9]+$ ]]; then
        print_error "Extension must be numeric: $EXTENSION"
        exit 1
    fi

    if [ -n "$EMAIL" ] && ! [[ $EMAIL =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
        print_error "Invalid email format: $EMAIL"
        exit 1
    fi

    print_status "Parameters validated successfully"
    print_status "Server IP: $SERVER_IP"
    print_status "Domain: $DOMAIN"
    print_status "SIP Username: $SIP_USERNAME"
    print_status "Extension: $EXTENSION"
    if [ -n "$EMAIL" ]; then
        print_status "Email: $EMAIL"
    fi
}

# Check if sshpass is installed
check_sshpass() {
    if ! command -v sshpass >/dev/null 2>&1; then
        print_error "sshpass is not installed"
        print_error "Install it with: sudo apt-get install sshpass"
        exit 1
    fi
    print_status "sshpass is available"
}

# Execute remote command
remote_exec() {
    local command="$1"
    local description="$2"
    
    if [ -n "$description" ]; then
        print_status "$description"
    fi
    
    sshpass -p "$ADMIN_PASSWORD" ssh -o StrictHostKeyChecking=no "$ADMIN_USERNAME@$SERVER_IP" "$command"
}

# Copy file to remote server
remote_copy() {
    local local_file="$1"
    local remote_path="$2"
    local description="$3"
    
    if [ -n "$description" ]; then
        print_status "$description"
    fi
    
    sshpass -p "$ADMIN_PASSWORD" scp -o StrictHostKeyChecking=no "$local_file" "$ADMIN_USERNAME@$SERVER_IP:$remote_path"
}

# Test SSH connection
test_ssh_connection() {
    print_header "Testing SSH connection..."
    
    if sshpass -p "$ADMIN_PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 "$ADMIN_USERNAME@$SERVER_IP" "echo 'SSH connection successful'" 2>/dev/null; then
        print_status "SSH connection successful"
    else
        print_error "SSH connection failed"
        exit 1
    fi
}

# Check if Asterisk is running on remote server
check_remote_asterisk() {
    print_header "Checking remote Asterisk status..."
    
    if remote_exec "systemctl is-active --quiet asterisk" "Checking if Asterisk is running"; then
        print_status "Asterisk is running on remote server"
    else
        print_error "Asterisk is not running on remote server"
        print_error "Please run the setup script first: ./remote_setup_sip_server.sh"
        exit 1
    fi
}

# Check if user already exists on remote server
check_user_exists() {
    print_header "Checking if user already exists..."
    
    if remote_exec "grep -q '^\[$SIP_USERNAME\]' /etc/asterisk/sip.conf 2>/dev/null" "Checking SIP username"; then
        print_error "SIP user '$SIP_USERNAME' already exists on remote server"
        exit 1
    fi

    if remote_exec "grep -q '^\[$EXTENSION\]' /etc/asterisk/sip.conf 2>/dev/null" "Checking extension"; then
        print_error "Extension '$EXTENSION' already exists on remote server"
        exit 1
    fi

    print_status "User and extension are available"
}

# Backup remote configuration
backup_remote_config() {
    print_header "Backing up remote configuration..."
    
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    remote_exec "cp /etc/asterisk/sip.conf /etc/asterisk/sip.conf.backup.$TIMESTAMP" "Backing up sip.conf"
    remote_exec "cp /etc/asterisk/voicemail.conf /etc/asterisk/voicemail.conf.backup.$TIMESTAMP" "Backing up voicemail.conf"
    
    print_status "Remote configuration backed up"
}

# Add SIP user to remote server
add_remote_sip_user() {
    print_header "Adding SIP user to remote server..."
    
    # Create temporary SIP user configuration
    cat > /tmp/sip_user_addition.txt << EOF

; SIP User: $SIP_USERNAME (Extension: $EXTENSION)
[$SIP_USERNAME](user_template)
secret=$SIP_PASSWORD
callerid="$SIP_USERNAME" <$EXTENSION>
mailbox=$EXTENSION@default

; Extension mapping
[$EXTENSION](user_template)
secret=$SIP_PASSWORD
callerid="$SIP_USERNAME" <$EXTENSION>
mailbox=$EXTENSION@default

EOF

    # Copy and append to remote sip.conf
    remote_copy "/tmp/sip_user_addition.txt" "/tmp/sip_user_addition.txt" "Copying user configuration"
    remote_exec "cat /tmp/sip_user_addition.txt >> /etc/asterisk/sip.conf" "Adding user to sip.conf"
    remote_exec "rm -f /tmp/sip_user_addition.txt" "Cleaning up temporary file"
    
    # Clean up local temporary file
    rm -f /tmp/sip_user_addition.txt
    
    print_status "SIP user added to remote server"
}

# Add voicemail box on remote server
add_remote_voicemail() {
    print_header "Adding voicemail box on remote server..."
    
    # Prepare email part
    EMAIL_PART=""
    if [ -n "$EMAIL" ]; then
        EMAIL_PART=",$EMAIL"
    fi
    
    # Add voicemail box
    remote_exec "sed -i '/^\[default\]/a $EXTENSION => $SIP_PASSWORD,$SIP_USERNAME$EMAIL_PART' /etc/asterisk/voicemail.conf" "Adding voicemail box"
    
    # Create user directory
    remote_exec "mkdir -p /var/spool/asterisk/voicemail/default/$EXTENSION" "Creating voicemail directory"
    remote_exec "chown asterisk:asterisk /var/spool/asterisk/voicemail/default/$EXTENSION" "Setting directory permissions"
    remote_exec "chmod 755 /var/spool/asterisk/voicemail/default/$EXTENSION" "Setting directory permissions"
    
    print_status "Voicemail box added on remote server"
}

# Reload Asterisk configuration on remote server
reload_remote_asterisk() {
    print_header "Reloading Asterisk configuration on remote server..."
    
    remote_exec "asterisk -rx 'sip reload'" "Reloading SIP configuration"
    remote_exec "asterisk -rx 'voicemail reload'" "Reloading voicemail configuration"
    remote_exec "asterisk -rx 'dialplan reload'" "Reloading dialplan"
    
    print_status "Asterisk configuration reloaded on remote server"
}

# Verify user creation on remote server
verify_remote_user() {
    print_header "Verifying user creation on remote server..."
    
    # Check if user appears in SIP peers
    if remote_exec "asterisk -rx 'sip show peer $SIP_USERNAME' | grep -q 'Name'" "Checking SIP user registration"; then
        print_status "SIP user '$SIP_USERNAME' is registered on remote server"
    else
        print_warning "SIP user '$SIP_USERNAME' may not be properly registered"
    fi
    
    # Check if extension appears in SIP peers
    if remote_exec "asterisk -rx 'sip show peer $EXTENSION' | grep -q 'Name'" "Checking extension registration"; then
        print_status "Extension '$EXTENSION' is registered on remote server"
    else
        print_warning "Extension '$EXTENSION' may not be properly registered"
    fi
}

# Generate user configuration summary
generate_summary() {
    print_header "User Configuration Summary"
    
    cat << EOF

=== SIP User Configuration ===
Username: $SIP_USERNAME
Password: $SIP_PASSWORD
Extension: $EXTENSION
Domain: $DOMAIN
Server: $SERVER_IP
$([ -n "$EMAIL" ] && echo "Email: $EMAIL")

=== Zoiper Configuration ===
Account Name: $SIP_USERNAME@$DOMAIN
Domain/Server: $SERVER_IP
Username: $SIP_USERNAME (or $EXTENSION)
Password: $SIP_PASSWORD
Port: 5060
Transport: UDP

=== Alternative Configuration ===
Username: $EXTENSION
Password: $SIP_PASSWORD
Server: $SERVER_IP

=== Test Commands ===
Check user status:
sshpass -p '$ADMIN_PASSWORD' ssh $ADMIN_USERNAME@$SERVER_IP 'asterisk -rx "sip show peers"'

Monitor logs:
sshpass -p '$ADMIN_PASSWORD' ssh $ADMIN_USERNAME@$SERVER_IP 'tail -f /var/log/asterisk/messages'

=== Test Numbers ===
*43 - Echo test
*60 - Speaking clock
*97 - Voicemail access

EOF

    print_status "User '$SIP_USERNAME' with extension '$EXTENSION' has been successfully created on remote server!"
}

# Main execution function
main() {
    print_header "Starting Remote SIP User Addition"
    
    validate_params "$@"
    check_sshpass
    test_ssh_connection
    check_remote_asterisk
    check_user_exists
    backup_remote_config
    add_remote_sip_user
    add_remote_voicemail
    reload_remote_asterisk
    verify_remote_user
    generate_summary
}

# Run main function with all arguments
main "$@"
