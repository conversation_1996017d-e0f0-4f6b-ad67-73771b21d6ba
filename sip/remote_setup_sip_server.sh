#!/bin/bash

# Remote SIP Server Setup Script using SSH
# This script deploys SIP server to a remote server using sshpass
# Usage: ./remote_setup_sip_server.sh <server_ip> <username> <password> <domain>

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[REMOTE SETUP]${NC} $1"
}

# Validate input parameters
validate_params() {
    if [ $# -ne 4 ]; then
        print_error "Usage: $0 <server_ip> <username> <password> <domain>"
        print_error "Example: $0 ************** root 'pvt@1234helloworld' oliviqo.com"
        exit 1
    fi

    SERVER_IP="$1"
    USERNAME="$2"
    PASSWORD="$3"
    DOMAIN="$4"

    # Validate IP address format
    if ! [[ $SERVER_IP =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        print_error "Invalid IP address format: $SERVER_IP"
        exit 1
    fi

    # Validate domain format
    if ! [[ $DOMAIN =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; then
        print_error "Invalid domain format: $DOMAIN"
        exit 1
    fi

    print_status "Parameters validated successfully"
    print_status "Server IP: $SERVER_IP"
    print_status "Username: $USERNAME"
    print_status "Domain: $DOMAIN"
}

# Check if sshpass is installed
check_sshpass() {
    if ! command -v sshpass >/dev/null 2>&1; then
        print_error "sshpass is not installed"
        print_error "Install it with: sudo apt-get install sshpass"
        exit 1
    fi
    print_status "sshpass is available"
}

# Test SSH connection
test_ssh_connection() {
    print_header "Testing SSH connection..."

    # Try multiple SSH connection methods
    local connection_success=false

    # Method 1: With explicit password authentication (disable key auth)
    if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o PreferredAuthentications=password -o PubkeyAuthentication=no -o PasswordAuthentication=yes -o ConnectTimeout=10 "$USERNAME@$SERVER_IP" "echo 'SSH connection successful'" 2>/dev/null; then
        connection_success=true
    # Method 2: Basic sshpass
    elif sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 "$USERNAME@$SERVER_IP" "echo 'SSH connection successful'" 2>/dev/null; then
        connection_success=true
    # Method 3: With password file (more secure for special characters)
    else
        local temp_pass_file=$(mktemp)
        echo "$PASSWORD" > "$temp_pass_file"
        chmod 600 "$temp_pass_file"

        if sshpass -f "$temp_pass_file" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 "$USERNAME@$SERVER_IP" "echo 'SSH connection successful'" 2>/dev/null; then
            connection_success=true
        fi

        rm -f "$temp_pass_file"
    fi

    if [ "$connection_success" = true ]; then
        print_status "SSH connection successful"
    else
        print_error "SSH connection failed"
        print_error "Please check:"
        print_error "1. Server IP address: $SERVER_IP"
        print_error "2. Username: $USERNAME"
        print_error "3. Password (try running: ./test_ssh_connection.sh $SERVER_IP $USERNAME 'PASSWORD')"
        print_error "4. SSH service running on server"
        print_error "5. Server firewall allows SSH connections"
        exit 1
    fi
}

# Execute remote command
remote_exec() {
    local command="$1"
    local description="$2"

    if [ -n "$description" ]; then
        print_status "$description"
    fi

    # Try multiple methods for executing remote commands
    if ! sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o PreferredAuthentications=password -o PubkeyAuthentication=no -o PasswordAuthentication=yes "$USERNAME@$SERVER_IP" "$command" 2>/dev/null; then
        # Try basic method as fallback
        if ! sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$USERNAME@$SERVER_IP" "$command" 2>/dev/null; then
            # Try with password file
            local temp_pass_file=$(mktemp)
            echo "$PASSWORD" > "$temp_pass_file"
            chmod 600 "$temp_pass_file"

            sshpass -f "$temp_pass_file" ssh -o StrictHostKeyChecking=no "$USERNAME@$SERVER_IP" "$command"
            local exit_code=$?
            rm -f "$temp_pass_file"
            return $exit_code
        fi
    fi
}

# Copy file to remote server
remote_copy() {
    local local_file="$1"
    local remote_path="$2"
    local description="$3"

    if [ -n "$description" ]; then
        print_status "$description"
    fi

    # Try multiple methods for copying files
    if ! sshpass -p "$PASSWORD" scp -o StrictHostKeyChecking=no -o PreferredAuthentications=password -o PubkeyAuthentication=no -o PasswordAuthentication=yes "$local_file" "$USERNAME@$SERVER_IP:$remote_path" 2>/dev/null; then
        # Try basic method as fallback
        if ! sshpass -p "$PASSWORD" scp -o StrictHostKeyChecking=no "$local_file" "$USERNAME@$SERVER_IP:$remote_path" 2>/dev/null; then
            # Try with password file
            local temp_pass_file=$(mktemp)
            echo "$PASSWORD" > "$temp_pass_file"
            chmod 600 "$temp_pass_file"

            sshpass -f "$temp_pass_file" scp -o StrictHostKeyChecking=no "$local_file" "$USERNAME@$SERVER_IP:$remote_path"
            local exit_code=$?
            rm -f "$temp_pass_file"
            return $exit_code
        fi
    fi
}

# Update remote system
update_remote_system() {
    print_header "Updating remote system packages..."
    
    remote_exec "apt-get update -y && apt-get upgrade -y" "Updating package lists and upgrading system"
    print_status "Remote system updated successfully"
}

# Install packages on remote server
install_remote_packages() {
    print_header "Installing packages on remote server..."
    
    # Core Asterisk packages for Ubuntu 24.04
    PACKAGES="asterisk asterisk-modules asterisk-config asterisk-core-sounds-en asterisk-core-sounds-en-wav fail2ban ufw nginx certbot python3-certbot-nginx curl wget openssl net-tools sshpass"
    
    remote_exec "DEBIAN_FRONTEND=noninteractive apt-get install -y $PACKAGES" "Installing SIP server packages"
    print_status "Packages installed successfully on remote server"
}

# Configure remote firewall
configure_remote_firewall() {
    print_header "Configuring remote firewall..."
    
    remote_exec "ufw --force reset" "Resetting firewall"
    remote_exec "ufw default deny incoming" "Setting default deny incoming"
    remote_exec "ufw default allow outgoing" "Setting default allow outgoing"
    
    # Allow SSH (important!)
    remote_exec "ufw allow ssh" "Allowing SSH"
    remote_exec "ufw allow 22/tcp" "Allowing SSH port 22"
    
    # Allow HTTP and HTTPS
    remote_exec "ufw allow 80/tcp" "Allowing HTTP"
    remote_exec "ufw allow 443/tcp" "Allowing HTTPS"
    
    # Allow SIP ports
    remote_exec "ufw allow 5060/udp comment 'SIP signaling UDP'" "Allowing SIP UDP"
    remote_exec "ufw allow 5060/tcp comment 'SIP signaling TCP'" "Allowing SIP TCP"
    remote_exec "ufw allow 5061/udp comment 'SIP TLS UDP'" "Allowing SIP TLS UDP"
    remote_exec "ufw allow 5061/tcp comment 'SIP TLS TCP'" "Allowing SIP TLS TCP"
    
    # Allow RTP ports
    remote_exec "ufw allow 10000:20000/udp comment 'RTP media streams'" "Allowing RTP ports"
    
    # Allow IAX2
    remote_exec "ufw allow 4569/udp comment 'IAX2'" "Allowing IAX2"
    
    # Enable firewall
    remote_exec "ufw --force enable" "Enabling firewall"
    
    print_status "Remote firewall configured successfully"
}

# Create SIP configuration on remote server
create_remote_sip_config() {
    print_header "Creating SIP configuration on remote server..."
    
    # Create temporary SIP configuration file
    cat > /tmp/sip.conf << EOF
[general]
context=default
allowoverlap=no
udpbindaddr=0.0.0.0:5060
tcpenable=yes
tcpbindaddr=0.0.0.0:5060
transport=udp,tcp
srvlookup=yes
domain=$DOMAIN
fromdomain=$DOMAIN
externip=$SERVER_IP

; Local network configuration
localnet=***********/***********
localnet=10.0.0.0/*********
localnet=**********/***********

; RTP Configuration
rtpstart=10000
rtpend=20000

; Security settings
alwaysauthreject=yes
allowguest=no

; Codecs
disallow=all
allow=ulaw
allow=alaw
allow=gsm
allow=g726
allow=g722

; NAT settings
nat=force_rport,comedia

; Registration settings
defaultexpiry=120
minexpiry=60
maxexpiry=3600

; Template for SIP users
[user_template](!)
type=friend
host=dynamic
context=internal
dtmfmode=rfc2833
disallow=all
allow=ulaw,alaw,gsm
nat=force_rport,comedia
qualify=yes
canreinvite=no

EOF

    # Copy configuration to remote server
    remote_copy "/tmp/sip.conf" "/etc/asterisk/sip.conf" "Copying SIP configuration"
    
    # Clean up temporary file
    rm -f /tmp/sip.conf
    
    print_status "SIP configuration created on remote server"
}

# Create extensions configuration
create_remote_extensions_config() {
    print_header "Creating extensions configuration on remote server..."
    
    cat > /tmp/extensions.conf << EOF
[general]
static=yes
writeprotect=no
clearglobalvars=no

[globals]

[default]
exten => _X.,1,Hangup()

[internal]
; Internal extension dialing
exten => _1XXX,1,Dial(SIP/\${EXTEN},20)
exten => _1XXX,n,Voicemail(\${EXTEN}@default)
exten => _1XXX,n,Hangup()

; Voicemail access
exten => *97,1,VoiceMailMain(\${CALLERID(num)}@default)
exten => *97,n,Hangup()

; Echo test
exten => *43,1,Answer()
exten => *43,n,Playback(demo-echotest)
exten => *43,n,Echo()
exten => *43,n,Playback(demo-echodone)
exten => *43,n,Hangup()

; Speaking clock
exten => *60,1,Answer()
exten => *60,n,SayUnixTime()
exten => *60,n,Hangup()

[outbound]
include => internal

EOF

    remote_copy "/tmp/extensions.conf" "/etc/asterisk/extensions.conf" "Copying extensions configuration"
    rm -f /tmp/extensions.conf
    
    print_status "Extensions configuration created on remote server"
}

# Create voicemail configuration
create_remote_voicemail_config() {
    print_header "Creating voicemail configuration on remote server..."
    
    cat > /tmp/voicemail.conf << EOF
[general]
format=wav49|gsm|wav
serveremail=asterisk@$DOMAIN
attach=yes
skipms=3000
maxsilence=10
silencethreshold=128
maxlogins=3

[zonemessages]
eastern=America/New_York|'vm-received' Q 'digits/at' IMp
central=America/Chicago|'vm-received' Q 'digits/at' IMp
mountain=America/Denver|'vm-received' Q 'digits/at' IMp
pacific=America/Los_Angeles|'vm-received' Q 'digits/at' IMp

[default]
; Voicemail boxes will be added here

EOF

    remote_copy "/tmp/voicemail.conf" "/etc/asterisk/voicemail.conf" "Copying voicemail configuration"
    rm -f /tmp/voicemail.conf
    
    print_status "Voicemail configuration created on remote server"
}

# Start services on remote server
start_remote_services() {
    print_header "Starting services on remote server..."
    
    remote_exec "systemctl stop asterisk 2>/dev/null || true" "Stopping Asterisk if running"
    remote_exec "systemctl start asterisk" "Starting Asterisk"
    remote_exec "systemctl enable asterisk" "Enabling Asterisk"
    
    remote_exec "systemctl restart fail2ban" "Restarting fail2ban"
    remote_exec "systemctl enable fail2ban" "Enabling fail2ban"
    
    # Verify services
    if remote_exec "systemctl is-active --quiet asterisk" "Checking Asterisk status"; then
        print_status "✓ Asterisk is running on remote server"
    else
        print_warning "⚠ Asterisk may not be running properly"
    fi
    
    print_status "Services started on remote server"
}

# Create management scripts on remote server
create_remote_management_scripts() {
    print_header "Creating management scripts on remote server..."
    
    remote_exec "mkdir -p /opt/sip-management" "Creating management directory"
    
    # Create status script
    cat > /tmp/sip-status.sh << 'EOF'
#!/bin/bash
echo "=== Asterisk Status ==="
systemctl status asterisk --no-pager -l
echo ""
echo "=== SIP Peers ==="
asterisk -rx "sip show peers"
echo ""
echo "=== Active Channels ==="
asterisk -rx "core show channels"
EOF

    remote_copy "/tmp/sip-status.sh" "/opt/sip-management/sip-status.sh" "Copying status script"
    remote_exec "chmod +x /opt/sip-management/sip-status.sh" "Making status script executable"
    rm -f /tmp/sip-status.sh
    
    print_status "Management scripts created on remote server"
}

# Main execution function
main() {
    print_header "Starting Remote SIP Server Setup"
    
    validate_params "$@"
    check_sshpass
    test_ssh_connection
    
    update_remote_system
    install_remote_packages
    configure_remote_firewall
    
    # Backup existing configuration
    remote_exec "mkdir -p /etc/asterisk/backup_$(date +%Y%m%d_%H%M%S) && cp -r /etc/asterisk/* /etc/asterisk/backup_$(date +%Y%m%d_%H%M%S)/ 2>/dev/null || true" "Backing up existing configuration"
    
    create_remote_sip_config
    create_remote_extensions_config
    create_remote_voicemail_config
    start_remote_services
    create_remote_management_scripts
    
    print_status "Remote SIP Server setup completed successfully!"
    print_status "Server: $SERVER_IP"
    print_status "Domain: $DOMAIN"
    print_status "Username: $USERNAME"
    print_warning "Use remote_add_sip_user.sh to add SIP users"
    print_warning "Check status with: sshpass -p '$PASSWORD' ssh $USERNAME@$SERVER_IP '/opt/sip-management/sip-status.sh'"

    echo ""
    print_header "Next Steps:"
    print_status "1. Add SIP users: ./remote_add_sip_user.sh $SERVER_IP $USERNAME '$PASSWORD' $DOMAIN john johnpass 1001"
    print_status "2. Test connection with Zoiper using server IP: $SERVER_IP"
    print_status "3. Monitor logs: sshpass -p '$PASSWORD' ssh $USERNAME@$SERVER_IP 'tail -f /var/log/asterisk/messages'"

    # Create local record of deployment
    echo "REMOTE_SIP_DEPLOYMENT_RECORD" > /tmp/sip_deployment_record.txt
    echo "SERVER_IP=$SERVER_IP" >> /tmp/sip_deployment_record.txt
    echo "USERNAME=$USERNAME" >> /tmp/sip_deployment_record.txt
    echo "DOMAIN=$DOMAIN" >> /tmp/sip_deployment_record.txt
    echo "DEPLOYMENT_DATE=$(date)" >> /tmp/sip_deployment_record.txt

    print_status "Deployment record created for rollback purposes"
}

# Run main function with all arguments
main "$@"
