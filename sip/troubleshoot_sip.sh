#!/bin/bash

# SIP Connection Troubleshooting Script
# This script diagnoses and fixes common SIP connection issues
# Usage: sudo ./troubleshoot_sip.sh

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[CHECK]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_fix() {
    echo -e "${GREEN}[FIX]${NC} $1"
}

# Check if script is run as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root"
        exit 1
    fi
}

# Check Asterisk service status
check_asterisk_service() {
    print_header "Checking Asterisk service..."
    
    if systemctl is-active --quiet asterisk; then
        print_status "Asterisk service is running"
        
        # Check if Asterisk is responding
        if asterisk -rx "core show version" >/dev/null 2>&1; then
            print_status "Asterisk is responding to commands"
        else
            print_error "Asterisk service is running but not responding"
            print_fix "Restarting Asterisk service..."
            systemctl restart asterisk
            sleep 3
            if asterisk -rx "core show version" >/dev/null 2>&1; then
                print_status "Asterisk is now responding"
            else
                print_error "Asterisk still not responding - check logs"
            fi
        fi
    else
        print_error "Asterisk service is not running"
        print_fix "Starting Asterisk service..."
        systemctl start asterisk
        sleep 3
        if systemctl is-active --quiet asterisk; then
            print_status "Asterisk service started successfully"
        else
            print_error "Failed to start Asterisk service"
        fi
    fi
}

# Check network ports
check_network_ports() {
    print_header "Checking network ports..."
    
    # Check if SIP port 5060 is listening
    if netstat -ln 2>/dev/null | grep -q ":5060 "; then
        print_status "SIP port 5060 is listening"
        
        # Show what's listening on 5060
        LISTENING=$(netstat -ln | grep ":5060 " | head -1)
        print_info "Port 5060: $LISTENING"
    else
        print_error "SIP port 5060 is not listening"
        print_fix "Checking Asterisk SIP configuration..."
        
        # Check if SIP is enabled in modules.conf
        if grep -q "load => chan_sip.so" /etc/asterisk/modules.conf 2>/dev/null; then
            print_status "SIP module is configured to load"
        else
            print_warning "SIP module may not be configured"
        fi
    fi
    
    # Check RTP ports
    if netstat -ln 2>/dev/null | grep -q ":1000[0-9] "; then
        print_status "RTP ports appear to be available"
    else
        print_warning "RTP ports may not be properly configured"
    fi
}

# Check firewall configuration
check_firewall() {
    print_header "Checking firewall configuration..."
    
    if command -v ufw >/dev/null 2>&1; then
        UFW_STATUS=$(ufw status)
        
        if echo "$UFW_STATUS" | grep -q "Status: active"; then
            print_status "UFW firewall is active"
            
            # Check SIP ports
            if echo "$UFW_STATUS" | grep -q "5060"; then
                print_status "SIP port 5060 is allowed"
            else
                print_error "SIP port 5060 is not allowed in firewall"
                print_fix "Adding SIP port 5060 to firewall..."
                ufw allow 5060/udp
                ufw allow 5060/tcp
                print_status "SIP port 5060 added to firewall"
            fi
            
            # Check RTP ports
            if echo "$UFW_STATUS" | grep -q "10000:20000"; then
                print_status "RTP ports are allowed"
            else
                print_error "RTP ports are not allowed in firewall"
                print_fix "Adding RTP ports to firewall..."
                ufw allow 10000:20000/udp
                print_status "RTP ports added to firewall"
            fi
        else
            print_warning "UFW firewall is not active"
            print_info "Consider enabling firewall with: ufw enable"
        fi
    else
        print_warning "UFW not found - checking iptables..."
        if command -v iptables >/dev/null 2>&1; then
            print_info "iptables is available"
        else
            print_warning "No firewall tools found"
        fi
    fi
}

# Check SIP configuration
check_sip_configuration() {
    print_header "Checking SIP configuration..."
    
    if [ -f "/etc/asterisk/sip.conf" ]; then
        print_status "SIP configuration file exists"
        
        # Check UDP binding
        if grep -q "udpbindaddr=0.0.0.0:5060" /etc/asterisk/sip.conf; then
            print_status "SIP UDP binding is configured correctly"
        else
            print_warning "SIP UDP binding may not be configured correctly"
            print_info "Expected: udpbindaddr=0.0.0.0:5060"
            print_info "Found: $(grep "udpbindaddr" /etc/asterisk/sip.conf || echo "Not found")"
        fi
        
        # Check external IP
        if grep -q "externip=" /etc/asterisk/sip.conf; then
            EXTERN_IP=$(grep "externip=" /etc/asterisk/sip.conf | head -1 | cut -d'=' -f2)
            print_status "External IP configured: $EXTERN_IP"
        else
            print_warning "External IP not configured"
            print_info "This may cause NAT issues"
        fi
        
        # Check domain
        if grep -q "domain=" /etc/asterisk/sip.conf; then
            DOMAIN=$(grep "domain=" /etc/asterisk/sip.conf | head -1 | cut -d'=' -f2)
            print_status "SIP domain configured: $DOMAIN"
        else
            print_warning "SIP domain not configured"
        fi
        
    else
        print_error "SIP configuration file not found"
    fi
}

# Check SIP peers and their status
check_sip_peers() {
    print_header "Checking SIP peers..."
    
    if systemctl is-active --quiet asterisk; then
        # Get peer information
        PEER_OUTPUT=$(asterisk -rx "sip show peers" 2>/dev/null)
        
        if echo "$PEER_OUTPUT" | grep -q "Name/username"; then
            print_status "SIP peers table is accessible"
            
            # Count peers
            PEER_COUNT=$(echo "$PEER_OUTPUT" | grep -c "^[0-9a-zA-Z]" || echo "0")
            print_info "Total SIP peers: $PEER_COUNT"
            
            # Check for UNKNOWN status
            UNKNOWN_COUNT=$(echo "$PEER_OUTPUT" | grep -c "UNKNOWN" || echo "0")
            if [ "$UNKNOWN_COUNT" -gt 0 ]; then
                print_warning "Found $UNKNOWN_COUNT peers with UNKNOWN status"
                print_info "UNKNOWN status usually means the client hasn't registered yet"
                
                # Show peers with UNKNOWN status
                echo "$PEER_OUTPUT" | grep "UNKNOWN" | while read line; do
                    print_info "UNKNOWN peer: $line"
                done
            fi
            
            # Check for registered peers
            REGISTERED_COUNT=$(echo "$PEER_OUTPUT" | grep -c "OK" || echo "0")
            if [ "$REGISTERED_COUNT" -gt 0 ]; then
                print_status "Found $REGISTERED_COUNT registered peers"
            else
                print_warning "No peers are currently registered"
            fi
            
        else
            print_error "Cannot access SIP peers information"
        fi
    else
        print_error "Asterisk is not running"
    fi
}

# Check Asterisk logs for errors
check_asterisk_logs() {
    print_header "Checking Asterisk logs for recent errors..."
    
    if [ -f "/var/log/asterisk/messages" ]; then
        print_status "Asterisk log file found"
        
        # Check for recent errors (last 50 lines)
        RECENT_ERRORS=$(tail -50 /var/log/asterisk/messages | grep -i "error\|warning\|failed" | tail -5)
        
        if [ -n "$RECENT_ERRORS" ]; then
            print_warning "Recent errors/warnings found:"
            echo "$RECENT_ERRORS" | while read line; do
                print_info "$line"
            done
        else
            print_status "No recent errors found in logs"
        fi
        
        # Check for registration attempts
        RECENT_REGISTRATIONS=$(tail -50 /var/log/asterisk/messages | grep -i "registration\|register" | tail -3)
        if [ -n "$RECENT_REGISTRATIONS" ]; then
            print_info "Recent registration attempts:"
            echo "$RECENT_REGISTRATIONS" | while read line; do
                print_info "$line"
            done
        fi
        
    else
        print_warning "Asterisk log file not found at /var/log/asterisk/messages"
        
        # Check alternative log locations
        if [ -f "/var/log/asterisk/full" ]; then
            print_info "Found alternative log at /var/log/asterisk/full"
        fi
    fi
}

# Generate Zoiper configuration guide
generate_zoiper_config() {
    print_header "Generating Zoiper configuration guide..."
    
    # Get server IP from sip.conf
    if [ -f "/etc/asterisk/sip.conf" ]; then
        SERVER_IP=$(grep "externip=" /etc/asterisk/sip.conf | head -1 | cut -d'=' -f2 2>/dev/null || echo "YOUR_SERVER_IP")
        DOMAIN=$(grep "domain=" /etc/asterisk/sip.conf | head -1 | cut -d'=' -f2 2>/dev/null || echo "YOUR_DOMAIN")
    else
        SERVER_IP="YOUR_SERVER_IP"
        DOMAIN="YOUR_DOMAIN"
    fi
    
    cat << EOF

=== ZOIPER CONFIGURATION GUIDE ===

For user 'john' with extension '1001':

Account Settings:
- Account Name: john@$DOMAIN
- Domain: $SERVER_IP (or $DOMAIN if DNS is configured)
- Username: john (or 1001)
- Password: [the password you set]

Advanced Settings:
- Transport: UDP (try TCP if UDP fails)
- Port: 5060
- Outbound Proxy: [leave empty]

Network Settings:
- STUN: Disabled (try enabling if behind NAT)
- ICE: Disabled
- Use rport: Enabled

Codec Settings:
- Preferred: ulaw, alaw, gsm

Troubleshooting Steps:
1. Try both 'john' and '1001' as username
2. Ensure your device can reach $SERVER_IP on port 5060
3. Check if your router/firewall blocks SIP traffic
4. Try TCP transport if UDP fails
5. Enable STUN if behind NAT

EOF
}

# Provide specific fixes for UNKNOWN status
fix_unknown_status() {
    print_header "Fixing UNKNOWN peer status..."
    
    print_fix "Reloading SIP configuration..."
    asterisk -rx "sip reload" >/dev/null 2>&1
    
    print_fix "Restarting Asterisk service..."
    systemctl restart asterisk
    sleep 5
    
    print_info "Waiting for service to stabilize..."
    sleep 3
    
    # Check status again
    if systemctl is-active --quiet asterisk; then
        print_status "Asterisk restarted successfully"
        
        # Show current peer status
        print_info "Current SIP peer status:"
        asterisk -rx "sip show peers" 2>/dev/null | grep -E "Name/username|^[0-9a-zA-Z]" | head -10
    else
        print_error "Asterisk failed to restart"
    fi
}

# Main troubleshooting function
main() {
    print_header "Starting SIP Connection Troubleshooting"
    echo ""
    
    check_root
    check_asterisk_service
    check_network_ports
    check_firewall
    check_sip_configuration
    check_sip_peers
    check_asterisk_logs
    
    echo ""
    print_header "Applying fixes..."
    fix_unknown_status
    
    echo ""
    generate_zoiper_config
    
    echo ""
    print_status "Troubleshooting completed!"
    print_info "If issues persist, check:"
    print_info "1. Client firewall settings"
    print_info "2. Router/NAT configuration"
    print_info "3. ISP SIP blocking"
    print_info "4. Asterisk logs: tail -f /var/log/asterisk/messages"
}

# Run main function
main "$@"
