#!/bin/bash

# SIP Connection Test Script
# This script tests SIP user connection and diagnoses issues
# Usage: ./test_sip_connection.sh <server_ip> <username> <password> <sip_user> <sip_pass>

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Validate input parameters
validate_params() {
    if [ $# -ne 5 ]; then
        print_error "Usage: $0 <server_ip> <ssh_username> <ssh_password> <sip_user> <sip_password>"
        print_error "Example: $0 ************* root 'Pvt@1234helloworld' john johnpass"
        exit 1
    fi

    SERVER_IP="$1"
    SSH_USERNAME="$2"
    SSH_PASSWORD="$3"
    SIP_USER="$4"
    SIP_PASSWORD="$5"

    print_status "Testing SIP connection for user: $SIP_USER"
    print_status "Server: $SERVER_IP"
}

# Execute remote command
remote_exec() {
    local command="$1"
    sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no -o PreferredAuthentications=password -o PubkeyAuthentication=no -o PasswordAuthentication=yes "$SSH_USERNAME@$SERVER_IP" "$command" 2>/dev/null
}

# Test basic connectivity
test_basic_connectivity() {
    print_header "Testing Basic Connectivity"
    
    # Test ping
    if ping -c 1 -W 5 "$SERVER_IP" >/dev/null 2>&1; then
        print_status "Server is reachable via ping"
    else
        print_warning "Server is not responding to ping (may be normal)"
    fi
    
    # Test SSH
    if remote_exec "echo 'SSH OK'" >/dev/null 2>&1; then
        print_status "SSH connection working"
    else
        print_error "SSH connection failed"
        return 1
    fi
}

# Test SIP ports
test_sip_ports() {
    print_header "Testing SIP Ports"
    
    # Test from client side
    if timeout 5 bash -c "</dev/tcp/$SERVER_IP/5060" 2>/dev/null; then
        print_status "SIP port 5060 is reachable from client"
    else
        print_error "SIP port 5060 is NOT reachable from client"
        print_error "This is likely the main issue!"
    fi
    
    # Test from server side
    local server_ports=$(remote_exec "netstat -tulpn | grep 5060")
    if [ -n "$server_ports" ]; then
        print_status "SIP port 5060 is listening on server"
        print_info "$server_ports"
    else
        print_error "SIP port 5060 is NOT listening on server"
    fi
}

# Test Asterisk service
test_asterisk_service() {
    print_header "Testing Asterisk Service"
    
    if remote_exec "systemctl is-active --quiet asterisk"; then
        print_status "Asterisk service is running"
    else
        print_error "Asterisk service is not running"
        return 1
    fi
    
    if remote_exec "asterisk -rx 'core show version' >/dev/null 2>&1"; then
        print_status "Asterisk is responding to commands"
    else
        print_error "Asterisk is not responding to commands"
    fi
}

# Test SIP configuration
test_sip_configuration() {
    print_header "Testing SIP Configuration"
    
    # Check if user exists
    local user_config=$(remote_exec "grep -A5 '^\[$SIP_USER\]' /etc/asterisk/sip.conf")
    if [ -n "$user_config" ]; then
        print_status "SIP user '$SIP_USER' is configured"
        print_info "User configuration:"
        echo "$user_config" | while read line; do
            print_info "  $line"
        done
    else
        print_error "SIP user '$SIP_USER' is NOT configured"
        return 1
    fi
    
    # Check domains
    local domains=$(remote_exec "grep '^domain=' /etc/asterisk/sip.conf")
    if [ -n "$domains" ]; then
        print_status "Configured domains:"
        echo "$domains" | while read line; do
            print_info "  $line"
        done
    else
        print_warning "No domains configured"
    fi
    
    # Check external IP
    local externip=$(remote_exec "grep '^externip=' /etc/asterisk/sip.conf")
    if [ -n "$externip" ]; then
        print_status "External IP: $externip"
    else
        print_warning "No external IP configured"
    fi
}

# Test firewall
test_firewall() {
    print_header "Testing Firewall Configuration"
    
    local ufw_status=$(remote_exec "ufw status 2>/dev/null || echo 'UFW not available'")
    
    if echo "$ufw_status" | grep -q "Status: active"; then
        print_status "UFW firewall is active"
        
        if echo "$ufw_status" | grep -q "5060"; then
            print_status "SIP port 5060 is allowed in firewall"
        else
            print_error "SIP port 5060 is NOT allowed in firewall"
            print_error "This could be blocking SIP connections!"
        fi
    else
        print_warning "UFW firewall is not active or not available"
    fi
    
    # Check iptables
    local iptables_rules=$(remote_exec "iptables -L INPUT -n | grep 5060 || echo 'No specific SIP rules'")
    print_info "Iptables SIP rules: $iptables_rules"
}

# Test SIP peers status
test_sip_peers() {
    print_header "Testing SIP Peers Status"
    
    local peers=$(remote_exec "asterisk -rx 'sip show peers'")
    if [ -n "$peers" ]; then
        print_status "SIP peers table:"
        echo "$peers" | while read line; do
            if [[ "$line" == *"$SIP_USER"* ]] || [[ "$line" == *"Name/username"* ]]; then
                if [[ "$line" == *"UNKNOWN"* ]]; then
                    print_warning "  $line"
                elif [[ "$line" == *"OK"* ]]; then
                    print_status "  $line"
                else
                    print_info "  $line"
                fi
            fi
        done
    else
        print_error "Could not get SIP peers information"
    fi
}

# Test with SIP client simulation
test_sip_registration() {
    print_header "Testing SIP Registration Simulation"
    
    # Start monitoring logs
    print_info "Starting log monitoring..."
    remote_exec "timeout 10 tail -f /var/log/asterisk/messages.log 2>/dev/null | grep -i 'registration\|failed\|auth' &" || true
    
    # Try to simulate SIP registration using netcat or telnet
    print_info "Attempting to test SIP port connectivity..."
    
    # Test raw TCP connection
    if command -v nc >/dev/null 2>&1; then
        print_info "Testing raw TCP connection to SIP port..."
        if timeout 5 nc -z "$SERVER_IP" 5060 2>/dev/null; then
            print_status "TCP connection to SIP port successful"
        else
            print_error "TCP connection to SIP port failed"
        fi
    fi
    
    # Test UDP connection
    if command -v nc >/dev/null 2>&1; then
        print_info "Testing UDP connection to SIP port..."
        if timeout 5 nc -u -z "$SERVER_IP" 5060 2>/dev/null; then
            print_status "UDP connection to SIP port successful"
        else
            print_error "UDP connection to SIP port failed"
        fi
    fi
}

# Check recent logs for errors
check_recent_logs() {
    print_header "Checking Recent Asterisk Logs"
    
    local recent_logs=$(remote_exec "tail -20 /var/log/asterisk/messages.log 2>/dev/null | grep -E 'ERROR|WARNING|NOTICE.*failed' | tail -5")
    
    if [ -n "$recent_logs" ]; then
        print_warning "Recent errors/warnings:"
        echo "$recent_logs" | while read line; do
            print_warning "  $line"
        done
    else
        print_status "No recent errors found in logs"
    fi
}

# Generate diagnosis report
generate_diagnosis() {
    print_header "Connection Diagnosis Summary"
    
    echo ""
    echo "=== DIAGNOSIS REPORT ==="
    echo ""
    
    # Test port connectivity from client
    if timeout 5 bash -c "</dev/tcp/$SERVER_IP/5060" 2>/dev/null; then
        echo "✓ SIP port 5060 is reachable from your location"
    else
        echo "✗ SIP port 5060 is NOT reachable from your location"
        echo "  → This is likely the main issue causing timeouts"
        echo "  → Possible causes:"
        echo "    - Server firewall blocking port 5060"
        echo "    - Your ISP blocking SIP traffic"
        echo "    - Network routing issues"
        echo "    - Cloud provider firewall rules"
    fi
    
    # Check if Asterisk is running
    if remote_exec "systemctl is-active --quiet asterisk" 2>/dev/null; then
        echo "✓ Asterisk service is running on server"
    else
        echo "✗ Asterisk service is not running on server"
    fi
    
    # Check if user is configured
    if remote_exec "grep -q '^\[$SIP_USER\]' /etc/asterisk/sip.conf" 2>/dev/null; then
        echo "✓ SIP user '$SIP_USER' is configured"
    else
        echo "✗ SIP user '$SIP_USER' is not configured"
    fi
    
    echo ""
    echo "=== RECOMMENDED ACTIONS ==="
    echo ""
    
    if ! timeout 5 bash -c "</dev/tcp/$SERVER_IP/5060" 2>/dev/null; then
        echo "1. Check server firewall:"
        echo "   sshpass -p '$SSH_PASSWORD' ssh $SSH_USERNAME@$SERVER_IP 'ufw allow 5060'"
        echo ""
        echo "2. Check cloud provider security groups/firewall"
        echo ""
        echo "3. Test from different network/location"
        echo ""
        echo "4. Contact your ISP about SIP traffic blocking"
    else
        echo "1. Check Zoiper configuration:"
        echo "   - Username: $SIP_USER"
        echo "   - Password: $SIP_PASSWORD"
        echo "   - Domain: $SERVER_IP"
        echo "   - Port: 5060"
        echo "   - Transport: UDP"
        echo "   - STUN: Disabled"
        echo ""
        echo "2. Try alternative SIP client (like Linphone)"
    fi
}

# Main execution function
main() {
    print_header "Starting Comprehensive SIP Connection Test"
    echo ""
    
    validate_params "$@"
    
    test_basic_connectivity
    test_sip_ports
    test_asterisk_service
    test_sip_configuration
    test_firewall
    test_sip_peers
    test_sip_registration
    check_recent_logs
    
    echo ""
    generate_diagnosis
}

# Run main function with all arguments
main "$@"
