#!/bin/bash

# SIP Server Setup Script for Ubuntu 24.04+
# Optimized for Ubuntu 24.04 Noble Numbat package structure
# Usage: ./setup_sip_server_ubuntu24.sh <server_ip> <username> <password> <domain>

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

# Check if script is run as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root"
        exit 1
    fi
}

# Validate Ubuntu version
check_ubuntu_version() {
    print_header "Checking Ubuntu version..."
    
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        if [[ "$ID" == "ubuntu" ]]; then
            UBUNTU_VERSION=$(echo $VERSION_ID)
            print_status "Ubuntu version: $UBUNTU_VERSION"
            
            if [[ "$UBUNTU_VERSION" < "24.04" ]]; then
                print_warning "This script is optimized for Ubuntu 24.04+. Use setup_sip_server.sh for older versions."
                read -p "Continue anyway? (y/N): " -n 1 -r
                echo
                if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                    exit 1
                fi
            fi
        else
            print_error "This script is designed for Ubuntu systems"
            exit 1
        fi
    else
        print_error "Cannot determine OS version"
        exit 1
    fi
}

# Validate input parameters
validate_params() {
    if [ $# -ne 4 ]; then
        print_error "Usage: $0 <server_ip> <username> <password> <domain>"
        print_error "Example: $0 ************* admin mypassword example.com"
        exit 1
    fi

    SERVER_IP="$1"
    USERNAME="$2"
    PASSWORD="$3"
    DOMAIN="$4"

    # Validate IP address format
    if ! [[ $SERVER_IP =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        print_error "Invalid IP address format: $SERVER_IP"
        exit 1
    fi

    # Validate domain format
    if ! [[ $DOMAIN =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; then
        print_error "Invalid domain format: $DOMAIN"
        exit 1
    fi

    print_status "Parameters validated successfully"
    print_status "Server IP: $SERVER_IP"
    print_status "Username: $USERNAME"
    print_status "Domain: $DOMAIN"
}

# Update system packages
update_system() {
    print_header "Updating system packages..."
    
    # Clean any problematic repositories first
    apt-get clean
    
    # Update package lists
    if apt-get update -y; then
        print_status "Package lists updated successfully"
    else
        print_warning "Some repository issues detected, but continuing..."
    fi
    
    # Upgrade system
    apt-get upgrade -y
    print_status "System updated successfully"
}

# Install packages optimized for Ubuntu 24.04
install_packages() {
    print_header "Installing packages for Ubuntu 24.04..."
    
    # Core Asterisk packages (consolidated in Ubuntu 24.04)
    CORE_PACKAGES=(
        "asterisk"
        "asterisk-modules"
        "asterisk-config"
        "asterisk-core-sounds-en"
        "asterisk-core-sounds-en-wav"
    )
    
    # Security and web packages
    SECURITY_PACKAGES=(
        "fail2ban"
        "ufw"
        "nginx"
        "certbot"
        "python3-certbot-nginx"
    )
    
    # Utility packages
    UTILITY_PACKAGES=(
        "curl"
        "wget"
        "openssl"
        "net-tools"
    )
    
    # Install core Asterisk packages
    print_status "Installing core Asterisk packages..."
    for package in "${CORE_PACKAGES[@]}"; do
        if apt-get install -y "$package"; then
            print_status "✓ Installed: $package"
        else
            print_error "✗ Failed to install: $package"
            exit 1
        fi
    done
    
    # Install security packages
    print_status "Installing security packages..."
    for package in "${SECURITY_PACKAGES[@]}"; do
        if apt-get install -y "$package"; then
            print_status "✓ Installed: $package"
        else
            print_warning "⚠ Could not install: $package"
        fi
    done
    
    # Install utility packages
    print_status "Installing utility packages..."
    for package in "${UTILITY_PACKAGES[@]}"; do
        if apt-get install -y "$package"; then
            print_status "✓ Installed: $package"
        else
            print_warning "⚠ Could not install: $package"
        fi
    done
    
    print_status "Package installation completed successfully"
}

# Configure firewall
configure_firewall() {
    print_header "Configuring firewall..."
    
    # Reset UFW to default state
    ufw --force reset
    
    # Set default policies
    ufw default deny incoming
    ufw default allow outgoing
    
    # Allow SSH (important!)
    ufw allow ssh
    ufw allow 22/tcp
    
    # Allow HTTP and HTTPS
    ufw allow 80/tcp
    ufw allow 443/tcp
    
    # Allow SIP ports
    ufw allow 5060/udp comment 'SIP signaling UDP'
    ufw allow 5060/tcp comment 'SIP signaling TCP'
    ufw allow 5061/udp comment 'SIP TLS UDP'
    ufw allow 5061/tcp comment 'SIP TLS TCP'
    
    # Allow RTP ports (media)
    ufw allow 10000:20000/udp comment 'RTP media streams'
    
    # Allow IAX2 (Inter-Asterisk eXchange)
    ufw allow 4569/udp comment 'IAX2'
    
    # Enable UFW
    ufw --force enable
    
    print_status "Firewall configured successfully"
    ufw status verbose
}

# Backup original configuration
backup_config() {
    print_header "Backing up original Asterisk configuration..."
    
    BACKUP_DIR="/etc/asterisk/backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    if [ -d "/etc/asterisk" ]; then
        cp -r /etc/asterisk/* "$BACKUP_DIR/" 2>/dev/null || true
        print_status "Configuration backed up to: $BACKUP_DIR"
    else
        print_warning "No existing Asterisk configuration found"
    fi
}

# Configure SIP for Ubuntu 24.04
configure_sip() {
    print_header "Configuring SIP settings for Ubuntu 24.04..."
    
    # Create optimized sip.conf for Ubuntu 24.04
    cat > /etc/asterisk/sip.conf << EOF
[general]
context=default
allowoverlap=no
udpbindaddr=0.0.0.0:5060
tcpenable=yes
tcpbindaddr=0.0.0.0:5060
transport=udp,tcp
srvlookup=yes
domain=$DOMAIN
fromdomain=$DOMAIN
externip=$SERVER_IP

; Local network configuration
localnet=***********/***********
localnet=10.0.0.0/*********
localnet=**********/***********

; RTP Configuration
rtpstart=10000
rtpend=20000

; Security settings
alwaysauthreject=yes
allowguest=no

; Codecs (optimized for Ubuntu 24.04)
disallow=all
allow=ulaw
allow=alaw
allow=gsm
allow=g726
allow=g722

; NAT settings
nat=force_rport,comedia

; Registration settings
defaultexpiry=120
minexpiry=60
maxexpiry=3600

; Template for SIP users
[user_template](!)
type=friend
host=dynamic
context=internal
dtmfmode=rfc2833
disallow=all
allow=ulaw,alaw,gsm
nat=force_rport,comedia
qualify=yes
canreinvite=no

EOF

    print_status "SIP configuration created for Ubuntu 24.04"
}

# Configure extensions
configure_extensions() {
    print_header "Configuring extensions..."
    
    cat > /etc/asterisk/extensions.conf << EOF
[general]
static=yes
writeprotect=no
clearglobalvars=no

[globals]

[default]
exten => _X.,1,Hangup()

[internal]
; Internal extension dialing
exten => _1XXX,1,Dial(SIP/\${EXTEN},20)
exten => _1XXX,n,Voicemail(\${EXTEN}@default)
exten => _1XXX,n,Hangup()

; Voicemail access
exten => *97,1,VoiceMailMain(\${CALLERID(num)}@default)
exten => *97,n,Hangup()

; Echo test
exten => *43,1,Answer()
exten => *43,n,Playback(demo-echotest)
exten => *43,n,Echo()
exten => *43,n,Playback(demo-echodone)
exten => *43,n,Hangup()

; Speaking clock
exten => *60,1,Answer()
exten => *60,n,SayUnixTime()
exten => *60,n,Hangup()

[outbound]
include => internal

EOF

    print_status "Extensions configuration created"
}

# Configure voicemail
configure_voicemail() {
    print_header "Configuring voicemail..."
    
    cat > /etc/asterisk/voicemail.conf << EOF
[general]
format=wav49|gsm|wav
serveremail=asterisk@$DOMAIN
attach=yes
skipms=3000
maxsilence=10
silencethreshold=128
maxlogins=3

[zonemessages]
eastern=America/New_York|'vm-received' Q 'digits/at' IMp
central=America/Chicago|'vm-received' Q 'digits/at' IMp
mountain=America/Denver|'vm-received' Q 'digits/at' IMp
pacific=America/Los_Angeles|'vm-received' Q 'digits/at' IMp

[default]
; Voicemail boxes will be added here

EOF

    print_status "Voicemail configuration created"
}

# Start services
start_services() {
    print_header "Starting and enabling services..."
    
    # Stop asterisk if running
    systemctl stop asterisk 2>/dev/null || true
    
    # Start and enable asterisk
    systemctl start asterisk
    systemctl enable asterisk
    
    # Start and enable fail2ban
    systemctl restart fail2ban
    systemctl enable fail2ban
    
    print_status "Services started and enabled"
    
    # Check service status
    if systemctl is-active --quiet asterisk; then
        print_status "✓ Asterisk is running"
    else
        print_error "✗ Asterisk failed to start"
    fi
    
    if systemctl is-active --quiet fail2ban; then
        print_status "✓ Fail2ban is running"
    else
        print_warning "⚠ Fail2ban may not be running properly"
    fi
}

# Main execution function
main() {
    print_header "Starting SIP Server Setup for Ubuntu 24.04"
    
    check_root
    check_ubuntu_version
    validate_params "$@"
    
    update_system
    install_packages
    configure_firewall
    backup_config
    configure_sip
    configure_extensions
    configure_voicemail
    start_services
    
    print_status "SIP Server setup completed successfully!"
    print_status "Domain: $DOMAIN"
    print_status "Server IP: $SERVER_IP"
    print_status "Admin Username: $USERNAME"
    print_warning "Use add_sip_user.sh to add SIP users"
    print_warning "Check status with: systemctl status asterisk"
    print_warning "Asterisk CLI: asterisk -r"
}

# Run main function with all arguments
main "$@"
