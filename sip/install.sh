#!/bin/bash

# SIP Server Scripts Installation
# This script prepares all SIP management scripts for use
# Usage: ./install.sh

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[INSTALL]${NC} $1"
}

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Make all scripts executable
make_executable() {
    print_header "Making scripts executable..."
    
    local scripts=(
        "setup_sip_server.sh"
        "add_sip_user.sh"
        "validate_sip_config.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [ -f "$SCRIPT_DIR/$script" ]; then
            chmod +x "$SCRIPT_DIR/$script"
            print_status "Made executable: $script"
        else
            print_warning "Script not found: $script"
        fi
    done
}

# Display usage information
show_usage() {
    print_header "SIP Server Management Scripts"
    
    cat << EOF

=== Available Scripts ===

1. setup_sip_server.sh - Complete SIP server setup
   Usage: sudo ./setup_sip_server.sh <server_ip> <admin_username> <admin_password> <domain>
   Example: sudo ./setup_sip_server.sh ************* admin mypassword sip.example.com

2. add_sip_user.sh - Add new SIP users
   Usage: sudo ./add_sip_user.sh <server_ip> <admin_username> <admin_password> <domain> <sip_username> <sip_password> [extension] [email]
   Example: sudo ./add_sip_user.sh ************* admin mypassword sip.example.com john johnpass 1001 <EMAIL>

3. validate_sip_config.sh - Validate SIP server configuration
   Usage: sudo ./validate_sip_config.sh

=== Quick Start Guide ===

Step 1: Setup your SIP server
   sudo ./setup_sip_server.sh YOUR_SERVER_IP admin YOUR_PASSWORD YOUR_DOMAIN

Step 2: Add your first user
   sudo ./add_sip_user.sh YOUR_SERVER_IP admin YOUR_PASSWORD YOUR_DOMAIN username userpass

Step 3: Validate configuration
   sudo ./validate_sip_config.sh

=== Important Notes ===

• All scripts must be run as root (use sudo)
• Make sure your server has a static IP address
• Ensure your domain points to your server IP
• Open required firewall ports (scripts handle this automatically)
• Keep your admin credentials secure

=== Network Requirements ===

The following ports will be opened automatically:
• 5060/UDP & TCP - SIP signaling
• 5061/UDP & TCP - SIP TLS (secure)
• 10000-20000/UDP - RTP media streams
• 4569/UDP - IAX2

=== After Installation ===

Management tools will be available in /opt/sip-management/:
• sip-status.sh - Check server status
• list-users.sh - List all SIP users
• remove-user.sh - Remove SIP users

Access Asterisk CLI: sudo asterisk -r

=== Support ===

For detailed documentation, see README.md
For troubleshooting, run: sudo ./validate_sip_config.sh

EOF
}

# Check system requirements
check_requirements() {
    print_header "Checking system requirements..."
    
    # Check if running on supported OS
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        if [[ "$ID" == "ubuntu" ]] || [[ "$ID" == "debian" ]]; then
            print_status "Supported OS detected: $PRETTY_NAME"
        else
            print_warning "OS not officially supported: $PRETTY_NAME"
            print_warning "Scripts are designed for Ubuntu/Debian systems"
        fi
    else
        print_warning "Cannot determine OS version"
    fi
    
    # Check if running as root for installation
    if [[ $EUID -eq 0 ]]; then
        print_warning "Running as root - this is only needed for actual SIP server setup"
        print_warning "This installation script can be run as regular user"
    fi
    
    # Check available disk space
    AVAILABLE_SPACE=$(df . | tail -1 | awk '{print $4}')
    if [ "$AVAILABLE_SPACE" -gt 1048576 ]; then  # 1GB in KB
        print_status "Sufficient disk space available"
    else
        print_warning "Low disk space - ensure at least 1GB free for SIP server"
    fi
}

# Create example configuration file
create_example_config() {
    print_header "Creating example configuration..."
    
    cat > "$SCRIPT_DIR/example_config.txt" << EOF
# SIP Server Configuration Example
# Copy and modify these values for your setup

# Server Configuration
SERVER_IP=*************          # Your server's IP address
ADMIN_USERNAME=admin             # Admin username for management
ADMIN_PASSWORD=secure_password   # Strong admin password
DOMAIN=sip.example.com          # Your SIP domain

# Example Setup Command
sudo ./setup_sip_server.sh \$SERVER_IP \$ADMIN_USERNAME \$ADMIN_PASSWORD \$DOMAIN

# Example User Addition
sudo ./add_sip_user.sh \$SERVER_IP \$ADMIN_USERNAME \$ADMIN_PASSWORD \$DOMAIN john johnpass 1001 <EMAIL>

# Security Notes:
# - Use strong passwords
# - Keep admin credentials secure
# - Regularly update the system
# - Monitor fail2ban logs
# - Use SSL/TLS for production

EOF

    print_status "Example configuration created: example_config.txt"
}

# Main installation function
main() {
    print_header "Installing SIP Server Management Scripts"
    echo ""
    
    check_requirements
    make_executable
    create_example_config
    
    echo ""
    print_status "Installation completed successfully!"
    print_status "All scripts are now ready to use"
    
    echo ""
    show_usage
}

# Run main function
main "$@"
