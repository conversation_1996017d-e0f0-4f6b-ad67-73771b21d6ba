#!/bin/bash

# SIP User Management Script
# This script adds new SIP users to the Asterisk server
# Usage: ./add_sip_user.sh <server_ip> <admin_username> <admin_password> <domain> <sip_username> <sip_password> [extension] [email]

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[USER SETUP]${NC} $1"
}

# Check if script is run as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root"
        exit 1
    fi
}

# Validate input parameters
validate_params() {
    if [ $# -lt 6 ] || [ $# -gt 8 ]; then
        print_error "Usage: $0 <server_ip> <admin_username> <admin_password> <domain> <sip_username> <sip_password> [extension] [email]"
        print_error "Example: $0 ************* admin mypassword example.com john johnpass 1001 <EMAIL>"
        exit 1
    fi

    SERVER_IP="$1"
    ADMIN_USERNAME="$2"
    ADMIN_PASSWORD="$3"
    DOMAIN="$4"
    SIP_USERNAME="$5"
    SIP_PASSWORD="$6"
    EXTENSION="${7:-}"
    EMAIL="${8:-}"

    # Auto-generate extension if not provided
    if [ -z "$EXTENSION" ]; then
        # Find next available extension starting from 1001
        EXTENSION=1001
        while grep -q "^\[$EXTENSION\]" /etc/asterisk/sip.conf 2>/dev/null; do
            ((EXTENSION++))
        done
    fi

    # Validate IP address format
    if ! [[ $SERVER_IP =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        print_error "Invalid IP address format: $SERVER_IP"
        exit 1
    fi

    # Validate domain format
    if ! [[ $DOMAIN =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; then
        print_error "Invalid domain format: $DOMAIN"
        exit 1
    fi

    # Validate extension format (should be numeric)
    if ! [[ $EXTENSION =~ ^[0-9]+$ ]]; then
        print_error "Extension must be numeric: $EXTENSION"
        exit 1
    fi

    # Validate email format if provided
    if [ -n "$EMAIL" ] && ! [[ $EMAIL =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
        print_error "Invalid email format: $EMAIL"
        exit 1
    fi

    print_status "Parameters validated successfully"
    print_status "Server IP: $SERVER_IP"
    print_status "Domain: $DOMAIN"
    print_status "SIP Username: $SIP_USERNAME"
    print_status "Extension: $EXTENSION"
    if [ -n "$EMAIL" ]; then
        print_status "Email: $EMAIL"
    fi
}

# Check if Asterisk is running
check_asterisk() {
    if ! systemctl is-active --quiet asterisk; then
        print_error "Asterisk is not running. Please start it first."
        exit 1
    fi
    print_status "Asterisk is running"
}

# Check if user already exists
check_user_exists() {
    if grep -q "^\[$SIP_USERNAME\]" /etc/asterisk/sip.conf 2>/dev/null; then
        print_error "SIP user '$SIP_USERNAME' already exists"
        exit 1
    fi

    if grep -q "^\[$EXTENSION\]" /etc/asterisk/sip.conf 2>/dev/null; then
        print_error "Extension '$EXTENSION' already exists"
        exit 1
    fi

    print_status "User and extension are available"
}

# Backup configuration files
backup_config() {
    print_header "Backing up configuration files..."
    
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    cp /etc/asterisk/sip.conf "/etc/asterisk/sip.conf.backup.$TIMESTAMP"
    cp /etc/asterisk/voicemail.conf "/etc/asterisk/voicemail.conf.backup.$TIMESTAMP"
    
    print_status "Configuration files backed up"
}

# Add SIP user to sip.conf
add_sip_user() {
    print_header "Adding SIP user to configuration..."
    
    # Add user to sip.conf
    cat >> /etc/asterisk/sip.conf << EOF

; SIP User: $SIP_USERNAME (Extension: $EXTENSION)
[$SIP_USERNAME](user_template)
secret=$SIP_PASSWORD
callerid="$SIP_USERNAME" <$EXTENSION>
mailbox=$EXTENSION@default

; Extension mapping
[$EXTENSION](user_template)
secret=$SIP_PASSWORD
callerid="$SIP_USERNAME" <$EXTENSION>
mailbox=$EXTENSION@default

EOF

    print_status "SIP user added to sip.conf"
}

# Add voicemail box
add_voicemail() {
    print_header "Adding voicemail box..."
    
    # Prepare email part
    EMAIL_PART=""
    if [ -n "$EMAIL" ]; then
        EMAIL_PART=",$EMAIL"
    fi
    
    # Add voicemail box to voicemail.conf
    if ! grep -q "^$EXTENSION =>" /etc/asterisk/voicemail.conf; then
        sed -i "/^\[default\]/a $EXTENSION => $SIP_PASSWORD,$SIP_USERNAME$EMAIL_PART" /etc/asterisk/voicemail.conf
        print_status "Voicemail box added for extension $EXTENSION"
    else
        print_warning "Voicemail box for extension $EXTENSION already exists"
    fi
}

# Create user directory and greeting
setup_user_directory() {
    print_header "Setting up user directory..."
    
    USER_DIR="/var/spool/asterisk/voicemail/default/$EXTENSION"
    mkdir -p "$USER_DIR"
    chown asterisk:asterisk "$USER_DIR"
    chmod 755 "$USER_DIR"
    
    print_status "User directory created: $USER_DIR"
}

# Reload Asterisk configuration
reload_asterisk() {
    print_header "Reloading Asterisk configuration..."
    
    # Reload SIP configuration
    asterisk -rx "sip reload" >/dev/null 2>&1
    
    # Reload voicemail configuration
    asterisk -rx "voicemail reload" >/dev/null 2>&1
    
    # Reload dialplan
    asterisk -rx "dialplan reload" >/dev/null 2>&1
    
    print_status "Asterisk configuration reloaded"
}

# Verify user creation
verify_user() {
    print_header "Verifying user creation..."
    
    # Check if user appears in SIP peers
    if asterisk -rx "sip show peer $SIP_USERNAME" | grep -q "Name"; then
        print_status "SIP user '$SIP_USERNAME' is registered in Asterisk"
    else
        print_warning "SIP user '$SIP_USERNAME' may not be properly registered"
    fi
    
    # Check if extension appears in SIP peers
    if asterisk -rx "sip show peer $EXTENSION" | grep -q "Name"; then
        print_status "Extension '$EXTENSION' is registered in Asterisk"
    else
        print_warning "Extension '$EXTENSION' may not be properly registered"
    fi
}

# Generate user configuration summary
generate_summary() {
    print_header "User Configuration Summary"
    
    cat << EOF

=== SIP User Configuration ===
Username: $SIP_USERNAME
Password: $SIP_PASSWORD
Extension: $EXTENSION
Domain: $DOMAIN
Server: $SERVER_IP
$([ -n "$EMAIL" ] && echo "Email: $EMAIL")

=== SIP Client Configuration ===
SIP Server: $SERVER_IP
Domain: $DOMAIN
Username: $SIP_USERNAME
Password: $SIP_PASSWORD
Port: 5060 (UDP/TCP)

=== Alternative Configuration (using extension) ===
Username: $EXTENSION
Password: $SIP_PASSWORD

=== Voicemail Access ===
Dial *97 from your extension to access voicemail
Default voicemail password: $SIP_PASSWORD

=== Test Numbers ===
*43 - Echo test
*60 - Speaking clock

EOF

    print_status "User '$SIP_USERNAME' with extension '$EXTENSION' has been successfully created!"
}

# Create user management functions
create_user_functions() {
    # Create a simple script to list users
    cat > /opt/sip-management/list-users.sh << 'EOF'
#!/bin/bash
echo "=== SIP Users ==="
asterisk -rx "sip show peers" | grep -E "^[0-9]|^[a-zA-Z]" | head -20
echo ""
echo "=== Voicemail Boxes ==="
grep "^[0-9]" /etc/asterisk/voicemail.conf | head -20
EOF

    chmod +x /opt/sip-management/list-users.sh
    
    # Create a script to remove users
    cat > /opt/sip-management/remove-user.sh << 'EOF'
#!/bin/bash
if [ $# -ne 1 ]; then
    echo "Usage: $0 <username_or_extension>"
    exit 1
fi

USER="$1"
echo "This will remove user/extension: $USER"
echo "Are you sure? (y/N)"
read -r confirm

if [[ $confirm =~ ^[Yy]$ ]]; then
    # Remove from sip.conf
    sed -i "/^\[$USER\]/,/^$/d" /etc/asterisk/sip.conf
    # Remove from voicemail.conf
    sed -i "/^$USER =>/d" /etc/asterisk/voicemail.conf
    # Remove voicemail directory
    rm -rf "/var/spool/asterisk/voicemail/default/$USER"
    # Reload Asterisk
    asterisk -rx "sip reload"
    asterisk -rx "voicemail reload"
    echo "User $USER removed successfully"
else
    echo "Operation cancelled"
fi
EOF

    chmod +x /opt/sip-management/remove-user.sh
}

# Main execution function
main() {
    print_header "Starting SIP User Addition"
    
    check_root
    validate_params "$@"
    check_asterisk
    check_user_exists
    backup_config
    add_sip_user
    add_voicemail
    setup_user_directory
    reload_asterisk
    verify_user
    create_user_functions
    generate_summary
}

# Run main function with all arguments
main "$@"
