#!/bin/bash

# Repository Fix Script
# This script fixes common repository issues before SIP server setup
# Usage: sudo ./fix_repositories.sh

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[FIX]${NC} $1"
}

# Check if script is run as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root"
        exit 1
    fi
}

# Fix legacy GPG keys
fix_legacy_keys() {
    print_header "Fixing legacy GPG keys..."
    
    # Fix Yarn repository key
    if [ -f "/etc/apt/sources.list.d/yarn.list" ]; then
        print_status "Fixing Yarn repository key..."
        curl -sS https://dl.yarnpkg.com/debian/pubkey.gpg | gpg --dearmor | tee /usr/share/keyrings/yarn.gpg >/dev/null
        echo "deb [signed-by=/usr/share/keyrings/yarn.gpg] https://dl.yarnpkg.com/debian/ stable main" > /etc/apt/sources.list.d/yarn.list
        print_status "Yarn repository key fixed"
    fi
    
    # Fix MongoDB repository key
    if [ -f "/etc/apt/sources.list.d/mongodb-org-6.0.list" ]; then
        print_status "Fixing MongoDB repository key..."
        curl -fsSL https://pgp.mongodb.com/server-6.0.asc | gpg --dearmor -o /usr/share/keyrings/mongodb-server-6.0.gpg
        echo "deb [arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-6.0.gpg] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/6.0 multiverse" > /etc/apt/sources.list.d/mongodb-org-6.0.list
        print_status "MongoDB repository key fixed"
    fi
}

# Remove problematic repositories
remove_problematic_repos() {
    print_header "Removing problematic repositories..."
    
    # Remove netspeed PPA that doesn't have noble release
    if [ -f "/etc/apt/sources.list.d/fixnix-ubuntu-netspeed-noble.list" ]; then
        print_status "Removing problematic netspeed PPA..."
        rm -f /etc/apt/sources.list.d/fixnix-ubuntu-netspeed-noble.list
        print_status "Netspeed PPA removed"
    fi
    
    # Check for other problematic PPAs
    if ls /etc/apt/sources.list.d/*noble* 2>/dev/null; then
        print_warning "Found other Noble-specific repositories. You may need to review them manually."
        ls /etc/apt/sources.list.d/*noble*
    fi
}

# Clean apt cache
clean_apt_cache() {
    print_header "Cleaning apt cache..."
    
    apt-get clean
    rm -rf /var/lib/apt/lists/*
    print_status "Apt cache cleaned"
}

# Update package lists
update_package_lists() {
    print_header "Updating package lists..."
    
    if apt-get update; then
        print_status "Package lists updated successfully"
    else
        print_warning "Some repositories may still have issues, but continuing..."
    fi
}

# Main execution function
main() {
    print_header "Starting Repository Fix"
    
    check_root
    fix_legacy_keys
    remove_problematic_repos
    clean_apt_cache
    update_package_lists
    
    print_status "Repository issues fixed! You can now run the SIP server setup."
    print_status "Next step: sudo ./setup_sip_server.sh ************** root 'pvt@1234helloworld' oliviqo.com"
}

# Run main function
main "$@"
