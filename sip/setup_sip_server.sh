#!/bin/bash

# SIP Server Setup Script
# This script sets up a complete SIP server using Asterisk with domain support
# Usage: ./setup_sip_server.sh <server_ip> <username> <password> <domain>

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

# Check if script is run as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root"
        exit 1
    fi
}

# Validate input parameters
validate_params() {
    if [ $# -ne 4 ]; then
        print_error "Usage: $0 <server_ip> <username> <password> <domain>"
        print_error "Example: $0 ************* admin mypassword example.com"
        exit 1
    fi

    SERVER_IP="$1"
    USERNAME="$2"
    PASSWORD="$3"
    DOMAIN="$4"

    # Validate IP address format
    if ! [[ $SERVER_IP =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        print_error "Invalid IP address format: $SERVER_IP"
        exit 1
    fi

    # Validate domain format
    if ! [[ $DOMAIN =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; then
        print_error "Invalid domain format: $DOMAIN"
        exit 1
    fi

    print_status "Parameters validated successfully"
    print_status "Server IP: $SERVER_IP"
    print_status "Username: $USERNAME"
    print_status "Domain: $DOMAIN"
}

# Update system packages
update_system() {
    print_header "Updating system packages..."
    apt-get update -y
    apt-get upgrade -y
    print_status "System updated successfully"
}

# Install required packages
install_packages() {
    print_header "Installing required packages..."
    
    # Install Asterisk and related packages
    apt-get install -y asterisk asterisk-modules asterisk-config asterisk-core-sounds-en \
                       asterisk-core-sounds-en-wav asterisk-dahdi asterisk-voicemail \
                       asterisk-voicemail-odbcstorage asterisk-mysql asterisk-flite \
                       fail2ban ufw nginx certbot python3-certbot-nginx
    
    print_status "Packages installed successfully"
}

# Configure firewall
configure_firewall() {
    print_header "Configuring firewall..."
    
    # Enable UFW
    ufw --force enable
    
    # Allow SSH
    ufw allow ssh
    
    # Allow HTTP and HTTPS
    ufw allow 80/tcp
    ufw allow 443/tcp
    
    # Allow SIP ports
    ufw allow 5060/udp  # SIP signaling
    ufw allow 5060/tcp  # SIP signaling
    ufw allow 5061/udp  # SIP TLS
    ufw allow 5061/tcp  # SIP TLS
    
    # Allow RTP ports (media)
    ufw allow 10000:20000/udp
    
    # Allow IAX2 (Inter-Asterisk eXchange)
    ufw allow 4569/udp
    
    print_status "Firewall configured successfully"
}

# Backup original Asterisk configuration
backup_config() {
    print_header "Backing up original Asterisk configuration..."
    
    BACKUP_DIR="/etc/asterisk/backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    cp -r /etc/asterisk/* "$BACKUP_DIR/"
    
    print_status "Configuration backed up to: $BACKUP_DIR"
}

# Configure Asterisk SIP settings
configure_sip() {
    print_header "Configuring SIP settings..."
    
    # Create sip.conf
    cat > /etc/asterisk/sip.conf << EOF
[general]
context=default
allowoverlap=no
udpbindaddr=0.0.0.0:5060
tcpenable=yes
tcpbindaddr=0.0.0.0:5060
transport=udp,tcp
srvlookup=yes
domain=$DOMAIN
fromdomain=$DOMAIN
externip=$SERVER_IP
localnet=***********/***********
localnet=10.0.0.0/*********
localnet=**********/***********

; RTP Configuration
rtpstart=10000
rtpend=20000

; Security settings
alwaysauthreject=yes
allowguest=no

; Codecs
allow=ulaw
allow=alaw
allow=gsm
allow=g726
allow=g722
allow=speex

; NAT settings
nat=force_rport,comedia

; Registration settings
defaultexpiry=120
minexpiry=60
maxexpiry=3600

; Template for SIP users
[user_template](!)
type=friend
host=dynamic
context=internal
dtmfmode=rfc2833
disallow=all
allow=ulaw,alaw,gsm
nat=force_rport,comedia
qualify=yes
canreinvite=no

EOF

    print_status "SIP configuration created"
}

# Configure Asterisk extensions
configure_extensions() {
    print_header "Configuring extensions..."

    cat > /etc/asterisk/extensions.conf << EOF
[general]
static=yes
writeprotect=no
clearglobalvars=no

[globals]
CONSOLE=Console/dsp
IAXINFO=guest
TRUNK=Zap/G2
TRUNKMSD=1

[default]
exten => _X.,1,Hangup()

[internal]
; Internal extension dialing
exten => _1XXX,1,Dial(SIP/\${EXTEN},20)
exten => _1XXX,n,Voicemail(\${EXTEN}@default)
exten => _1XXX,n,Hangup()

; Voicemail access
exten => *97,1,VoiceMailMain(\${CALLERID(num)}@default)
exten => *97,n,Hangup()

; Echo test
exten => *43,1,Answer()
exten => *43,n,Playback(demo-echotest)
exten => *43,n,Echo()
exten => *43,n,Playback(demo-echodone)
exten => *43,n,Hangup()

; Speaking clock
exten => *60,1,Answer()
exten => *60,n,SayUnixTime()
exten => *60,n,Hangup()

[outbound]
; Add outbound routes here if needed
include => internal

EOF

    print_status "Extensions configuration created"
}

# Configure voicemail
configure_voicemail() {
    print_header "Configuring voicemail..."

    cat > /etc/asterisk/voicemail.conf << EOF
[general]
format=wav49|gsm|wav
serveremail=asterisk@$DOMAIN
attach=yes
skipms=3000
maxsilence=10
silencethreshold=128
maxlogins=3
emaildateformat=%A, %B %d, %Y at %r
pagerdateformat=%A, %B %d, %Y at %r
sendvoicemail=yes

[zonemessages]
eastern=America/New_York|'vm-received' Q 'digits/at' IMp
central=America/Chicago|'vm-received' Q 'digits/at' IMp
mountain=America/Denver|'vm-received' Q 'digits/at' IMp
pacific=America/Los_Angeles|'vm-received' Q 'digits/at' IMp

[default]
; Voicemail boxes will be added here by the add_user script

EOF

    print_status "Voicemail configuration created"
}

# Configure modules
configure_modules() {
    print_header "Configuring Asterisk modules..."

    cat > /etc/asterisk/modules.conf << EOF
[modules]
autoload=yes

; Core modules
load => res_musiconhold.so
load => app_dial.so
load => app_playback.so
load => app_voicemail.so
load => app_directory.so
load => app_confbridge.so
load => app_queue.so
load => chan_sip.so
load => pbx_config.so
load => res_agi.so
load => app_system.so
load => app_echo.so
load => app_milliwatt.so
load => func_callerid.so
load => func_logic.so
load => func_strings.so
load => func_math.so
load => func_global.so
load => func_channel.so

; Format modules
load => format_gsm.so
load => format_wav.so
load => format_pcm.so
load => format_g729.so
load => format_g726.so
load => format_sln.so
load => format_ulaw.so
load => format_alaw.so

; Codec modules
load => codec_gsm.so
load => codec_ulaw.so
load => codec_alaw.so
load => codec_g726.so

EOF

    print_status "Modules configuration created"
}

# Create SSL certificate for secure SIP
setup_ssl() {
    print_header "Setting up SSL certificate..."

    # Create self-signed certificate for now
    mkdir -p /etc/asterisk/keys

    openssl req -new -x509 -days 365 -nodes -out /etc/asterisk/keys/asterisk.pem \
                -keyout /etc/asterisk/keys/asterisk.key \
                -subj "/C=US/ST=State/L=City/O=Organization/CN=$DOMAIN"

    chmod 600 /etc/asterisk/keys/asterisk.key
    chmod 644 /etc/asterisk/keys/asterisk.pem

    print_status "SSL certificate created"
}

# Configure fail2ban for security
configure_fail2ban() {
    print_header "Configuring fail2ban for SIP security..."

    cat > /etc/fail2ban/jail.d/asterisk.conf << EOF
[asterisk-iptables]
enabled = true
filter = asterisk
action = iptables-allports[name=ASTERISK, protocol=all]
logpath = /var/log/asterisk/messages
maxretry = 5
bantime = 259200

[asterisk-security]
enabled = true
filter = asterisk-security
action = iptables-allports[name=ASTERISK-security, protocol=all]
logpath = /var/log/asterisk/messages
maxretry = 5
bantime = 259200

EOF

    # Create asterisk filter
    cat > /etc/fail2ban/filter.d/asterisk.conf << EOF
[Definition]
failregex = NOTICE.* .*: Registration from '.*' failed for '<HOST>:.*' - Wrong password
            NOTICE.* .*: Registration from '.*' failed for '<HOST>:.*' - No matching peer found
            NOTICE.* .*: Registration from '.*' failed for '<HOST>:.*' - Username/auth name mismatch
            NOTICE.* .*: Registration from '.*' failed for '<HOST>:.*' - Device does not match ACL
            NOTICE.* .*: Registration from '.*' failed for '<HOST>:.*' - Peer is not supposed to register
            NOTICE.* <HOST> failed to authenticate as '.*'
            NOTICE.* .*: No registration for peer '.*' \(from <HOST>\)
            NOTICE.* .*: Host <HOST> failed MD5 authentication for '.*' (.*)
            NOTICE.* .*: Failed to authenticate user .*@<HOST>.*

ignoreregex =

EOF

    systemctl restart fail2ban
    print_status "Fail2ban configured for Asterisk"
}

# Start and enable services
start_services() {
    print_header "Starting and enabling services..."

    systemctl stop asterisk 2>/dev/null || true
    systemctl start asterisk
    systemctl enable asterisk

    systemctl restart fail2ban
    systemctl enable fail2ban

    print_status "Services started and enabled"
}

# Create management scripts directory
create_management_scripts() {
    print_header "Creating management scripts..."

    mkdir -p /opt/sip-management

    # Create a simple status check script
    cat > /opt/sip-management/sip-status.sh << 'EOF'
#!/bin/bash
echo "=== Asterisk Status ==="
systemctl status asterisk --no-pager -l
echo ""
echo "=== SIP Peers ==="
asterisk -rx "sip show peers"
echo ""
echo "=== Active Channels ==="
asterisk -rx "core show channels"
EOF

    chmod +x /opt/sip-management/sip-status.sh

    print_status "Management scripts created in /opt/sip-management/"
}

# Main execution function
main() {
    print_header "Starting SIP Server Setup"

    check_root
    validate_params "$@"

    update_system
    install_packages
    configure_firewall
    backup_config
    configure_sip
    configure_extensions
    configure_voicemail
    configure_modules
    setup_ssl
    configure_fail2ban
    start_services
    create_management_scripts

    print_status "SIP Server setup completed successfully!"
    print_status "Domain: $DOMAIN"
    print_status "Server IP: $SERVER_IP"
    print_status "Admin Username: $USERNAME"
    print_warning "Please use the add_sip_user.sh script to add SIP users"
    print_warning "Check status with: /opt/sip-management/sip-status.sh"
    print_warning "Asterisk CLI: asterisk -r"
}

# Run main function with all arguments
main "$@"
