#!/bin/bash

# Local Asterisk Rollback Script
# This script removes Asterisk and related packages from your local system
# Usage: sudo ./rollback_local_asterisk.sh

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[ROLLBACK]${NC} $1"
}

# Check if script is run as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root (sudo)"
        exit 1
    fi
}

# Confirm rollback action
confirm_rollback() {
    print_header "Local Asterisk Rollback Confirmation"
    
    echo ""
    print_warning "This script will:"
    print_warning "1. Stop and disable Asterisk service"
    print_warning "2. Remove all Asterisk packages"
    print_warning "3. Remove Asterisk configuration files"
    print_warning "4. Remove Asterisk data directories"
    print_warning "5. Remove firewall rules for SIP"
    print_warning "6. Clean up related packages"
    echo ""
    
    read -p "Are you sure you want to proceed with the rollback? (yes/no): " -r
    if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
        print_status "Rollback cancelled by user"
        exit 0
    fi
    
    print_status "Proceeding with rollback..."
}

# Stop Asterisk services
stop_asterisk_services() {
    print_header "Stopping Asterisk services..."
    
    # Stop Asterisk service
    if systemctl is-active --quiet asterisk 2>/dev/null; then
        print_status "Stopping Asterisk service..."
        systemctl stop asterisk
        print_status "Asterisk service stopped"
    else
        print_status "Asterisk service is not running"
    fi
    
    # Disable Asterisk service
    if systemctl is-enabled --quiet asterisk 2>/dev/null; then
        print_status "Disabling Asterisk service..."
        systemctl disable asterisk
        print_status "Asterisk service disabled"
    else
        print_status "Asterisk service is not enabled"
    fi
}

# Remove Asterisk packages
remove_asterisk_packages() {
    print_header "Removing Asterisk packages..."
    
    # List of Asterisk-related packages to remove
    ASTERISK_PACKAGES=(
        "asterisk"
        "asterisk-modules"
        "asterisk-config"
        "asterisk-core-sounds-en"
        "asterisk-core-sounds-en-wav"
        "asterisk-dahdi"
        "asterisk-voicemail"
        "asterisk-voicemail-odbcstorage"
        "asterisk-mysql"
        "asterisk-flite"
        "asterisk-doc"
        "asterisk-dev"
    )
    
    # Remove packages that are installed
    for package in "${ASTERISK_PACKAGES[@]}"; do
        if dpkg -l | grep -q "^ii.*$package "; then
            print_status "Removing package: $package"
            apt-get remove --purge -y "$package" 2>/dev/null || print_warning "Could not remove $package"
        else
            print_status "Package not installed: $package"
        fi
    done
    
    # Remove any remaining asterisk packages
    print_status "Removing any remaining Asterisk packages..."
    apt-get autoremove --purge -y asterisk* 2>/dev/null || true
    
    print_status "Asterisk packages removed"
}

# Remove configuration files and directories
remove_asterisk_files() {
    print_header "Removing Asterisk configuration files and directories..."
    
    # List of directories to remove
    ASTERISK_DIRS=(
        "/etc/asterisk"
        "/var/lib/asterisk"
        "/var/spool/asterisk"
        "/var/log/asterisk"
        "/usr/share/asterisk"
        "/opt/sip-management"
    )
    
    for dir in "${ASTERISK_DIRS[@]}"; do
        if [ -d "$dir" ]; then
            print_status "Removing directory: $dir"
            rm -rf "$dir"
        else
            print_status "Directory not found: $dir"
        fi
    done
    
    # Remove asterisk user and group
    if id "asterisk" &>/dev/null; then
        print_status "Removing asterisk user..."
        userdel asterisk 2>/dev/null || true
    fi
    
    if getent group asterisk &>/dev/null; then
        print_status "Removing asterisk group..."
        groupdel asterisk 2>/dev/null || true
    fi
    
    print_status "Asterisk files and directories removed"
}

# Remove firewall rules
remove_firewall_rules() {
    print_header "Removing SIP firewall rules..."
    
    if command -v ufw >/dev/null 2>&1; then
        print_status "Removing UFW rules for SIP..."
        
        # Remove SIP-related rules
        ufw --force delete allow 5060/udp 2>/dev/null || true
        ufw --force delete allow 5060/tcp 2>/dev/null || true
        ufw --force delete allow 5061/udp 2>/dev/null || true
        ufw --force delete allow 5061/tcp 2>/dev/null || true
        ufw --force delete allow 10000:20000/udp 2>/dev/null || true
        ufw --force delete allow 4569/udp 2>/dev/null || true
        
        print_status "SIP firewall rules removed"
    else
        print_status "UFW not found, skipping firewall rule removal"
    fi
}

# Clean up package cache
cleanup_packages() {
    print_header "Cleaning up package cache..."
    
    # Clean package cache
    apt-get clean
    apt-get autoremove -y
    apt-get autoclean
    
    print_status "Package cache cleaned"
}

# Remove systemd service files
remove_systemd_files() {
    print_header "Removing systemd service files..."
    
    # List of potential systemd service files
    SYSTEMD_FILES=(
        "/etc/systemd/system/asterisk.service"
        "/lib/systemd/system/asterisk.service"
        "/usr/lib/systemd/system/asterisk.service"
    )
    
    for service_file in "${SYSTEMD_FILES[@]}"; do
        if [ -f "$service_file" ]; then
            print_status "Removing systemd service file: $service_file"
            rm -f "$service_file"
        fi
    done
    
    # Reload systemd daemon
    systemctl daemon-reload
    
    print_status "Systemd service files removed"
}

# Check for remaining Asterisk processes
check_remaining_processes() {
    print_header "Checking for remaining Asterisk processes..."

    # Look for actual asterisk daemon processes, not scripts containing "asterisk"
    ASTERISK_PROCESSES=$(ps aux | grep -E "/usr/sbin/asterisk|asterisk.*-f.*-v" | grep -v grep | wc -l)

    if [ "$ASTERISK_PROCESSES" -gt 0 ]; then
        print_warning "Found $ASTERISK_PROCESSES remaining Asterisk daemon processes:"
        ps aux | grep -E "/usr/sbin/asterisk|asterisk.*-f.*-v" | grep -v grep

        print_status "Killing remaining Asterisk daemon processes..."
        pkill -f "/usr/sbin/asterisk" 2>/dev/null || true
        sleep 2

        # Force kill if still running
        pkill -9 -f "/usr/sbin/asterisk" 2>/dev/null || true

        print_status "Remaining processes terminated"
    else
        print_status "No remaining Asterisk daemon processes found"
    fi
}

# Generate rollback report
generate_rollback_report() {
    print_header "Generating rollback report..."
    
    REPORT_FILE="/tmp/asterisk_rollback_report_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$REPORT_FILE" << EOF
=== Asterisk Local Rollback Report ===
Date: $(date)
System: $(uname -a)
User: $(whoami)

=== Actions Performed ===
1. Stopped and disabled Asterisk service
2. Removed Asterisk packages
3. Removed configuration files and directories
4. Removed firewall rules for SIP
5. Cleaned up package cache
6. Removed systemd service files
7. Terminated remaining processes

=== Directories Removed ===
- /etc/asterisk
- /var/lib/asterisk
- /var/spool/asterisk
- /var/log/asterisk
- /usr/share/asterisk
- /opt/sip-management

=== Packages Removed ===
- asterisk and all related packages
- asterisk-modules
- asterisk-config
- asterisk-core-sounds-en*

=== Firewall Rules Removed ===
- Port 5060/udp (SIP signaling)
- Port 5060/tcp (SIP signaling)
- Port 5061/udp (SIP TLS)
- Port 5061/tcp (SIP TLS)
- Ports 10000:20000/udp (RTP media)
- Port 4569/udp (IAX2)

=== System Status After Rollback ===
Asterisk Service: $(systemctl is-active asterisk 2>/dev/null || echo "not found")
Asterisk Packages: $(dpkg -l | grep -c asterisk || echo "0")
Asterisk Processes: $(ps aux | grep -c "[a]sterisk" || echo "0")

=== Notes ===
- Local system has been cleaned of Asterisk installation
- Remote SIP server (if deployed) is unaffected
- To reinstall, use the remote deployment scripts
- This rollback only affects the local machine

EOF

    print_status "Rollback report saved to: $REPORT_FILE"
    
    # Display summary
    cat "$REPORT_FILE"
}

# Main rollback function
main() {
    print_header "Starting Local Asterisk Rollback"
    
    check_root
    confirm_rollback
    
    stop_asterisk_services
    check_remaining_processes
    remove_asterisk_packages
    remove_asterisk_files
    remove_firewall_rules
    remove_systemd_files
    cleanup_packages
    
    generate_rollback_report
    
    echo ""
    print_status "Local Asterisk rollback completed successfully!"
    print_status "Your local system has been cleaned of Asterisk installation"
    print_warning "Remote SIP server (if deployed) is unaffected by this rollback"
    print_status "Use remote deployment scripts for future SIP server setup"
}

# Run main function
main "$@"
