#!/bin/bash

# Remote SIP Configuration Validation Script using SSH
# This script validates the SIP server configuration on remote server using sshpass
# Usage: ./remote_validate_sip.sh <server_ip> <username> <password>

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[CHECK]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Validate input parameters
validate_params() {
    if [ $# -ne 3 ]; then
        print_error "Usage: $0 <server_ip> <username> <password>"
        print_error "Example: $0 ************** root 'pvt@1234helloworld'"
        exit 1
    fi

    SERVER_IP="$1"
    USERNAME="$2"
    PASSWORD="$3"

    # Validate IP address format
    if ! [[ $SERVER_IP =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        print_error "Invalid IP address format: $SERVER_IP"
        exit 1
    fi

    print_status "Parameters validated successfully"
    print_status "Server IP: $SERVER_IP"
    print_status "Username: $USERNAME"
}

# Check if sshpass is installed
check_sshpass() {
    if ! command -v sshpass >/dev/null 2>&1; then
        print_error "sshpass is not installed"
        print_error "Install it with: sudo apt-get install sshpass"
        exit 1
    fi
    print_status "sshpass is available"
}

# Execute remote command
remote_exec() {
    local command="$1"
    local description="$2"

    if [ -n "$description" ]; then
        print_info "$description"
    fi

    sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o PreferredAuthentications=password -o PubkeyAuthentication=no -o PasswordAuthentication=yes "$USERNAME@$SERVER_IP" "$command" 2>/dev/null
}

# Test SSH connection
test_ssh_connection() {
    print_header "Testing SSH connection..."

    if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o PreferredAuthentications=password -o PubkeyAuthentication=no -o PasswordAuthentication=yes -o ConnectTimeout=10 "$USERNAME@$SERVER_IP" "echo 'SSH connection successful'" 2>/dev/null; then
        print_status "SSH connection successful"
    else
        print_error "SSH connection failed"
        exit 1
    fi
}

# Check Asterisk installation
check_asterisk_installation() {
    print_header "Checking Asterisk installation on remote server..."
    
    if remote_exec "command -v asterisk >/dev/null 2>&1" "Checking if Asterisk is installed"; then
        ASTERISK_VERSION=$(remote_exec "asterisk -V 2>/dev/null | head -1" "Getting Asterisk version")
        print_status "Asterisk is installed: $ASTERISK_VERSION"
    else
        print_error "Asterisk is not installed on remote server"
        return 1
    fi
}

# Check Asterisk service status
check_asterisk_status() {
    print_header "Checking Asterisk service status on remote server..."
    
    if remote_exec "systemctl is-active --quiet asterisk" "Checking if Asterisk service is running"; then
        print_status "Asterisk service is running"
        
        # Check if Asterisk is responding
        if remote_exec "asterisk -rx 'core show version' >/dev/null 2>&1" "Testing Asterisk responsiveness"; then
            print_status "Asterisk is responding to commands"
        else
            print_warning "Asterisk service is running but not responding"
        fi
    else
        print_error "Asterisk service is not running on remote server"
        return 1
    fi
}

# Check configuration files
check_config_files() {
    print_header "Checking configuration files on remote server..."
    
    local config_files=(
        "/etc/asterisk/sip.conf"
        "/etc/asterisk/extensions.conf"
        "/etc/asterisk/voicemail.conf"
    )
    
    for config_file in "${config_files[@]}"; do
        if remote_exec "[ -f '$config_file' ]" "Checking if $config_file exists"; then
            print_status "Configuration file exists: $config_file"
            
            # Check if file is readable
            if remote_exec "[ -r '$config_file' ]" "Checking if $config_file is readable"; then
                print_status "Configuration file is readable: $config_file"
            else
                print_warning "Configuration file is not readable: $config_file"
            fi
        else
            print_error "Configuration file missing: $config_file"
        fi
    done
}

# Check SIP configuration
check_sip_config() {
    print_header "Checking SIP configuration on remote server..."
    
    if remote_exec "[ -f '/etc/asterisk/sip.conf' ]" "Checking if sip.conf exists"; then
        # Check for basic SIP settings
        if remote_exec "grep -q 'udpbindaddr' /etc/asterisk/sip.conf" "Checking UDP binding"; then
            print_status "SIP UDP binding configured"
        else
            print_warning "SIP UDP binding not found in configuration"
        fi
        
        if remote_exec "grep -q 'domain=' /etc/asterisk/sip.conf" "Checking domain configuration"; then
            DOMAIN=$(remote_exec "grep 'domain=' /etc/asterisk/sip.conf | head -1 | cut -d'=' -f2" "Getting domain")
            print_status "SIP domain configured: $DOMAIN"
        else
            print_warning "SIP domain not configured"
        fi
        
        if remote_exec "grep -q 'externip=' /etc/asterisk/sip.conf" "Checking external IP"; then
            EXTERN_IP=$(remote_exec "grep 'externip=' /etc/asterisk/sip.conf | head -1 | cut -d'=' -f2" "Getting external IP")
            print_status "External IP configured: $EXTERN_IP"
        else
            print_warning "External IP not configured"
        fi
    fi
}

# Check SIP peers
check_sip_peers() {
    print_header "Checking SIP peers on remote server..."
    
    if remote_exec "systemctl is-active --quiet asterisk" "Verifying Asterisk is running"; then
        PEER_OUTPUT=$(remote_exec "asterisk -rx 'sip show peers' 2>/dev/null" "Getting SIP peers")
        
        if echo "$PEER_OUTPUT" | grep -q "Name/username"; then
            PEER_COUNT=$(echo "$PEER_OUTPUT" | grep -c "^[0-9a-zA-Z]" || echo "0")
            print_status "SIP peers configured: $PEER_COUNT"
            
            if [ "$PEER_COUNT" -gt 0 ]; then
                print_info "SIP Peers:"
                echo "$PEER_OUTPUT" | grep "^[0-9a-zA-Z]" | head -5 | while read line; do
                    print_info "  $line"
                done
                
                # Check for UNKNOWN status
                UNKNOWN_COUNT=$(echo "$PEER_OUTPUT" | grep -c "UNKNOWN" || echo "0")
                if [ "$UNKNOWN_COUNT" -gt 0 ]; then
                    print_warning "Found $UNKNOWN_COUNT peers with UNKNOWN status (not registered)"
                fi
                
                # Check for registered peers
                REGISTERED_COUNT=$(echo "$PEER_OUTPUT" | grep -c "OK" || echo "0")
                if [ "$REGISTERED_COUNT" -gt 0 ]; then
                    print_status "Found $REGISTERED_COUNT registered peers"
                else
                    print_warning "No peers are currently registered"
                fi
            fi
        else
            print_error "Cannot access SIP peers information"
        fi
    fi
}

# Check network ports
check_network_ports() {
    print_header "Checking network ports on remote server..."
    
    # Check if SIP port 5060 is listening
    if remote_exec "netstat -ln 2>/dev/null | grep -q ':5060 '" "Checking if port 5060 is listening"; then
        print_status "SIP port 5060 is listening"
        
        # Show what's listening on 5060
        LISTENING=$(remote_exec "netstat -ln | grep ':5060 ' | head -1" "Getting port 5060 details")
        print_info "Port 5060: $LISTENING"
    else
        print_error "SIP port 5060 is not listening"
    fi
    
    # Check RTP ports
    if remote_exec "netstat -ln 2>/dev/null | grep -q ':1000[0-9] '" "Checking RTP ports"; then
        print_status "RTP ports appear to be available"
    else
        print_warning "RTP ports may not be properly configured"
    fi
}

# Check firewall configuration
check_firewall() {
    print_header "Checking firewall configuration on remote server..."
    
    if remote_exec "command -v ufw >/dev/null 2>&1" "Checking if UFW is available"; then
        UFW_STATUS=$(remote_exec "ufw status" "Getting UFW status")
        
        if echo "$UFW_STATUS" | grep -q "Status: active"; then
            print_status "UFW firewall is active"
            
            # Check SIP ports
            if echo "$UFW_STATUS" | grep -q "5060"; then
                print_status "SIP port 5060 is allowed"
            else
                print_warning "SIP port 5060 may not be allowed in firewall"
            fi
            
            # Check RTP ports
            if echo "$UFW_STATUS" | grep -q "10000:20000"; then
                print_status "RTP ports are allowed"
            else
                print_warning "RTP ports may not be allowed in firewall"
            fi
        else
            print_warning "UFW firewall is not active"
        fi
    else
        print_warning "UFW not found on remote server"
    fi
}

# Check system resources
check_system_resources() {
    print_header "Checking system resources on remote server..."
    
    # Check disk space
    DISK_USAGE=$(remote_exec "df /var/spool/asterisk 2>/dev/null | tail -1 | awk '{print \$5}' | sed 's/%//' || echo '0'" "Checking disk usage")
    if [ "$DISK_USAGE" -lt 80 ]; then
        print_status "Disk space usage: ${DISK_USAGE}%"
    else
        print_warning "Disk space usage is high: ${DISK_USAGE}%"
    fi
    
    # Check memory usage
    MEMORY_USAGE=$(remote_exec "free | grep Mem | awk '{printf \"%.0f\", \$3/\$2 * 100.0}'" "Checking memory usage")
    if [ "$MEMORY_USAGE" -lt 80 ]; then
        print_status "Memory usage: ${MEMORY_USAGE}%"
    else
        print_warning "Memory usage is high: ${MEMORY_USAGE}%"
    fi
}

# Generate Zoiper configuration guide
generate_zoiper_config() {
    print_header "Generating Zoiper configuration guide..."
    
    # Get server configuration
    DOMAIN=$(remote_exec "grep 'domain=' /etc/asterisk/sip.conf | head -1 | cut -d'=' -f2 2>/dev/null || echo 'YOUR_DOMAIN'" "Getting domain")
    
    cat << EOF

=== ZOIPER CONFIGURATION GUIDE ===

Server Details:
- Server IP: $SERVER_IP
- Domain: $DOMAIN
- SIP Port: 5060

For any SIP user (example: john with extension 1001):

Account Settings:
- Account Name: john@$DOMAIN
- Domain: $SERVER_IP
- Username: john (or 1001)
- Password: [user's password]

Advanced Settings:
- Transport: UDP (try TCP if UDP fails)
- Port: 5060
- Outbound Proxy: [leave empty]

Network Settings:
- STUN: Disabled (enable if behind NAT)
- ICE: Disabled
- Use rport: Enabled

Troubleshooting:
1. Ensure device can reach $SERVER_IP on port 5060
2. Try both username and extension as login
3. Check firewall on client device
4. Enable STUN if behind NAT
5. Try TCP transport if UDP fails

Test Extensions:
- *43: Echo test
- *60: Speaking clock
- *97: Voicemail access

EOF
}

# Generate summary report
generate_summary() {
    print_header "Validation Summary"
    
    echo ""
    echo "=== Remote SIP Server Health Check Complete ==="
    echo ""
    
    if remote_exec "systemctl is-active --quiet asterisk" >/dev/null 2>&1; then
        echo "✓ Asterisk service is running"
    else
        echo "✗ Asterisk service is not running"
    fi
    
    if remote_exec "[ -f '/etc/asterisk/sip.conf' ]" >/dev/null 2>&1; then
        echo "✓ SIP configuration exists"
    else
        echo "✗ SIP configuration missing"
    fi
    
    PEER_COUNT=$(remote_exec "asterisk -rx 'sip show peers' 2>/dev/null | grep -c '^[0-9a-zA-Z]' || echo '0'" 2>/dev/null || echo "0")
    echo "ℹ SIP peers configured: $PEER_COUNT"
    
    VOICEMAIL_COUNT=$(remote_exec "grep -c '^[0-9]' /etc/asterisk/voicemail.conf 2>/dev/null || echo '0'" 2>/dev/null || echo "0")
    echo "ℹ Voicemail boxes: $VOICEMAIL_COUNT"
    
    echo ""
    echo "Remote server management:"
    echo "- SSH access: sshpass -p 'PASSWORD' ssh $USERNAME@$SERVER_IP"
    echo "- Check status: sshpass -p 'PASSWORD' ssh $USERNAME@$SERVER_IP '/opt/sip-management/sip-status.sh'"
    echo "- Monitor logs: sshpass -p 'PASSWORD' ssh $USERNAME@$SERVER_IP 'tail -f /var/log/asterisk/messages'"
}

# Main execution function
main() {
    print_header "Starting Remote SIP Configuration Validation"
    echo ""
    
    validate_params "$@"
    check_sshpass
    test_ssh_connection
    check_asterisk_installation
    check_asterisk_status
    check_config_files
    check_sip_config
    check_sip_peers
    check_network_ports
    check_firewall
    check_system_resources
    
    echo ""
    generate_zoiper_config
    
    echo ""
    generate_summary
}

# Run main function
main "$@"
