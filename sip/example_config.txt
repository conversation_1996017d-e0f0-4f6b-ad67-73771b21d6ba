# SIP Server Configuration Example
# Copy and modify these values for your setup

# Server Configuration
SERVER_IP=*************          # Your server's IP address
ADMIN_USERNAME=admin             # Admin username for management
ADMIN_PASSWORD=secure_password   # Strong admin password
DOMAIN=sip.example.com          # Your SIP domain

# Example Setup Command
sudo ./setup_sip_server.sh $SERVER_IP $ADMIN_USERNAME $ADMIN_PASSWORD $DOMAIN

# Example User Addition
sudo ./add_sip_user.sh $SERVER_IP $ADMIN_USERNAME $ADMIN_PASSWORD $DOMAIN john johnpass 1001 <EMAIL>

# Security Notes:
# - Use strong passwords
# - Keep admin credentials secure
# - Regularly update the system
# - Monitor fail2ban logs
# - Use SSL/TLS for production

