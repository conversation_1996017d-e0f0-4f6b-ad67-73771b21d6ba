#!/bin/bash

# SSH Connection Test Script
# This script helps debug SSH connection issues
# Usage: ./test_ssh_connection.sh <server_ip> <username> <password>

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

# Validate input parameters
validate_params() {
    if [ $# -ne 3 ]; then
        print_error "Usage: $0 <server_ip> <username> <password>"
        print_error "Example: $0 ************** root 'pvt@1234helloworld'"
        exit 1
    fi

    SERVER_IP="$1"
    USERNAME="$2"
    PASSWORD="$3"

    print_status "Testing connection to: $USERNAME@$SERVER_IP"
    print_status "Password length: ${#PASSWORD} characters"
}

# Check if sshpass is installed
check_sshpass() {
    if ! command -v sshpass >/dev/null 2>&1; then
        print_error "sshpass is not installed"
        print_error "Install it with: sudo apt-get install sshpass"
        exit 1
    fi
    print_status "sshpass is available"
}

# Test basic connectivity
test_connectivity() {
    print_header "Testing basic connectivity..."
    
    if ping -c 1 -W 5 "$SERVER_IP" >/dev/null 2>&1; then
        print_status "Server is reachable via ping"
    else
        print_warning "Server is not responding to ping (may be normal if ICMP is blocked)"
    fi
}

# Test SSH port
test_ssh_port() {
    print_header "Testing SSH port 22..."
    
    if timeout 10 bash -c "</dev/tcp/$SERVER_IP/22" 2>/dev/null; then
        print_status "SSH port 22 is open"
    else
        print_error "SSH port 22 is not accessible"
        print_error "Please check:"
        print_error "1. SSH service is running on the server"
        print_error "2. Firewall allows SSH connections"
        print_error "3. Server IP is correct"
        return 1
    fi
}

# Test SSH connection with different methods
test_ssh_methods() {
    print_header "Testing SSH connection methods..."
    
    # Method 1: Basic sshpass
    print_status "Method 1: Basic sshpass connection..."
    if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 "$USERNAME@$SERVER_IP" "echo 'Method 1 successful'" 2>/dev/null; then
        print_status "✓ Method 1: Basic sshpass - SUCCESS"
        return 0
    else
        print_warning "✗ Method 1: Basic sshpass - FAILED"
    fi
    
    # Method 2: With BatchMode
    print_status "Method 2: With BatchMode..."
    if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o BatchMode=no -o ConnectTimeout=10 "$USERNAME@$SERVER_IP" "echo 'Method 2 successful'" 2>/dev/null; then
        print_status "✓ Method 2: With BatchMode - SUCCESS"
        return 0
    else
        print_warning "✗ Method 2: With BatchMode - FAILED"
    fi
    
    # Method 3: With explicit authentication
    print_status "Method 3: With explicit authentication..."
    if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o PreferredAuthentications=password -o PubkeyAuthentication=no -o ConnectTimeout=10 "$USERNAME@$SERVER_IP" "echo 'Method 3 successful'" 2>/dev/null; then
        print_status "✓ Method 3: Explicit password auth - SUCCESS"
        return 0
    else
        print_warning "✗ Method 3: Explicit password auth - FAILED"
    fi
    
    # Method 4: Verbose mode for debugging
    print_status "Method 4: Verbose mode (for debugging)..."
    print_warning "Running SSH in verbose mode to see detailed error..."
    sshpass -p "$PASSWORD" ssh -v -o StrictHostKeyChecking=no -o ConnectTimeout=10 "$USERNAME@$SERVER_IP" "echo 'Method 4 successful'" 2>&1 | head -20
    
    return 1
}

# Test with password file (more secure)
test_password_file() {
    print_header "Testing with password file method..."
    
    # Create temporary password file
    TEMP_PASS_FILE=$(mktemp)
    echo "$PASSWORD" > "$TEMP_PASS_FILE"
    chmod 600 "$TEMP_PASS_FILE"
    
    if sshpass -f "$TEMP_PASS_FILE" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 "$USERNAME@$SERVER_IP" "echo 'Password file method successful'" 2>/dev/null; then
        print_status "✓ Password file method - SUCCESS"
        rm -f "$TEMP_PASS_FILE"
        return 0
    else
        print_warning "✗ Password file method - FAILED"
        rm -f "$TEMP_PASS_FILE"
        return 1
    fi
}

# Check for common issues
check_common_issues() {
    print_header "Checking for common issues..."
    
    # Check if password contains special characters
    if [[ "$PASSWORD" =~ [\'\"\\] ]]; then
        print_warning "Password contains special characters that may need escaping"
        print_warning "Characters found: quotes or backslashes"
    fi
    
    if [[ "$PASSWORD" =~ [@#\$%\^&\*] ]]; then
        print_warning "Password contains special shell characters"
        print_warning "This may require careful escaping"
    fi
    
    # Check password length
    if [ ${#PASSWORD} -lt 8 ]; then
        print_warning "Password is quite short (${#PASSWORD} characters)"
    fi
    
    if [ ${#PASSWORD} -gt 50 ]; then
        print_warning "Password is quite long (${#PASSWORD} characters)"
    fi
}

# Provide troubleshooting suggestions
provide_suggestions() {
    print_header "Troubleshooting Suggestions"
    
    cat << EOF

If SSH connection failed, try these solutions:

1. **Manual SSH Test:**
   ssh $USERNAME@$SERVER_IP
   (Enter password manually to verify credentials)

2. **Check SSH Service:**
   - Ensure SSH service is running on the server
   - Check if SSH is listening on port 22

3. **Firewall Issues:**
   - Server firewall may be blocking SSH
   - Your ISP may be blocking outbound SSH

4. **Password Issues:**
   - Try escaping special characters
   - Use single quotes around password
   - Consider using SSH keys instead

5. **Alternative Connection Methods:**
   - Try from a different network
   - Use VPN if available
   - Check if server requires specific SSH configuration

6. **Server-side Checks:**
   - SSH service: systemctl status ssh
   - SSH config: /etc/ssh/sshd_config
   - Authentication logs: /var/log/auth.log

EOF
}

# Generate connection command examples
generate_examples() {
    print_header "Working Connection Examples"
    
    cat << EOF

If connection works, use these commands:

1. **Remote SIP Setup:**
   ./remote_setup_sip_server.sh '$SERVER_IP' '$USERNAME' '$PASSWORD' 'oliviqo.com'

2. **Add SIP User:**
   ./remote_add_sip_user.sh '$SERVER_IP' '$USERNAME' '$PASSWORD' 'oliviqo.com' 'john' 'johnpass' '1001'

3. **Validate Setup:**
   ./remote_validate_sip.sh '$SERVER_IP' '$USERNAME' '$PASSWORD'

4. **Manual SSH Commands:**
   sshpass -p '$PASSWORD' ssh $USERNAME@$SERVER_IP 'command'

EOF
}

# Main execution function
main() {
    print_header "Starting SSH Connection Test"
    
    validate_params "$@"
    check_sshpass
    test_connectivity
    
    if test_ssh_port; then
        check_common_issues
        
        if test_ssh_methods || test_password_file; then
            print_status "SSH connection successful!"
            generate_examples
        else
            print_error "All SSH connection methods failed"
            provide_suggestions
        fi
    else
        print_error "SSH port is not accessible"
        provide_suggestions
    fi
}

# Run main function with all arguments
main "$@"
